package auth

import (
	"time"

	"as-api/as/foundations/db/entities"
	"as-api/as/pkg/context"
	"as-api/as/pkg/helpers/apiutil"
	"as-api/as/pkg/helpers/generator"
	"as-api/as/pkg/helpers/pointer"
	"as-api/as/pkg/jwt"

	"github.com/pkg/errors"
)

var _validateJWT = jwt.ValidateJWT

func (a *auth) GenJWT(ctx context.Context, claims Claims) (*AuthToken, error) {
	claims.Type = RefreshToken
	rftoken, err := jwt.NewJWT(makeClaims(claims, _refreshTokenExp), a.env.RJWTSecretKey)
	if err != nil {
		return nil, errors.Wrap(err, "new refresh token")
	}

	token := &entities.RefreshToken{
		AuthID: &claims.AID,
		Token:  rftoken.String,
	}
	if err := a.r.SaveRefreshToken(ctx, token); err != nil {
		return nil, errors.Wrap(err, "save refresh token")
	}

	claims.Type = AccessToken
	accessToken, err := jwt.NewJWT(makeClaims(claims, _accessTokenExp), a.env.JWTSecretKey)
	if err != nil {
		return nil, errors.Wrap(err, "new access token")
	}

	return &AuthToken{
		AccessToken:  accessToken.String,
		RefreshToken: rftoken.String,
	}, nil
}

func (a *auth) TokenAuthen(ctx context.Context, token string) (*Auth, error) {
	if _, err := _validateJWT(token, a.env.RJWTSecretKey); err != nil {
		return nil, errors.Wrap(err, "validate token")
	}

	au, err := a.r.FindOneByToken(ctx, token)
	if err != nil {
		return nil, errors.Wrap(err, "FindOneByToken")
	}

	return ParseAuthFromEntities(au), nil
}

func (a *auth) GenResetPasswordJWT(ctx context.Context, authID string) (string, error) {
	claims := Claims{
		AID:  authID,
		Type: ResetPasswordToken,
	}

	rstoken, err := jwt.NewJWT(makeClaims(claims, _resetPasswordTokenExp), a.env.ResetPasswordJWTSecretKey)
	if err != nil {
		return "", errors.Wrap(err, "new refresh token")
	}

	token := &entities.RefreshToken{
		AuthID: &claims.AID,
		Token:  rstoken.String,
	}

	if err := a.r.SaveRefreshToken(ctx, token); err != nil {
		return "", errors.Wrap(err, "create refresh token")
	}
	return token.Token, nil
}

func (a *auth) VerifyResetPasswordToken(ctx context.Context, token string) (*Auth, error) {
	claims, err := _validateJWT(token, a.env.ResetPasswordJWTSecretKey)
	if err != nil {
		return nil, errors.Wrap(err, "validate token")
	}

	var tokenClaims Claims
	if err := claims.DecodeClaims(&tokenClaims); err != nil {
		return nil, errors.Wrap(err, "parse claims")
	}

	if tokenClaims.Type != ResetPasswordToken {
		return nil, errors.New("invalid token type")
	}

	au, err := a.r.FindOneByToken(ctx, token)
	if err != nil {
		return nil, errors.Wrap(err, "FindOneByToken")
	}

	return ParseAuthFromEntities(au), nil
}

func (a *auth) GenSignupJWT(ctx context.Context, req SignupRequest) (string, error) {
	au, err := a.r.FindOneByUsername(ctx, req.Email)
	if err != nil && !errors.Is(err, apiutil.ErrResourceNotFound) {
		return "", errors.Wrap(err, "FindOneByUsername")
	}

	if au != nil && au.UserID != nil {
		return "", errors.Wrap(apiutil.ErrEmailAlreadyExists, "email already signed up")
	}

	claims := SignupClaims{
		Email:             &req.Email,
		Lang:              req.Lang,
		Type:              SignupToken,
		RegisterType:      req.Type,
		ReceiveNewsletter: req.ReceiveNewsletter,
		CountryCode:       req.CountryCode,
		RegionId:          req.RegionId,
	}

	token, err := jwt.NewJWT(makeSignupClaims(claims, _signupTokenExp), a.env.JWTSecretKey)
	if err != nil {
		return "", errors.Wrap(err, "new signup token")
	}

	return token.String, nil
}

func (a *auth) VerifySignupToken(ctx context.Context, token string) (*SignUpToken, error) {
	claims, err := _validateJWT(token, a.env.JWTSecretKey)
	if err != nil {
		return nil, errors.Wrap(err, "validate token")
	}

	var tokenClaims SignupClaims
	if err := claims.DecodeClaims(&tokenClaims); err != nil {
		return nil, errors.Wrap(err, "parse claims")
	}

	if tokenClaims.Type != SignupToken {
		return nil, errors.New("invalid token type")
	}

	if tokenClaims.Email == nil {
		return nil, errors.New("email is required")
	}

	email := *tokenClaims.Email

	au, err := a.r.FindOneByUsername(ctx, email)
	if err != nil && !errors.Is(err, apiutil.ErrResourceNotFound) {
		return nil, errors.Wrap(err, "FindOneByUsername")
	}

	if au == nil {
		accountID := generator.GenerateID(10)
		au = &entities.Auth{
			Username:  email,
			AccountID: &accountID,
		}
		if err := a.r.CreateAuth(ctx, au); err != nil {
			return nil, errors.Wrap(err, "CreateAuth")
		}

		return &SignUpToken{
			ID:        au.ID,
			Email:     email,
			AccountID: accountID,
		}, nil
	}

	if au.UserID != nil {
		return nil, errors.Wrap(apiutil.ErrAlreadyUpdated, "email already signed up")
	}

	return &SignUpToken{
		ID:        au.ID,
		Email:     email,
		AccountID: pointer.Safe(au.AccountID),
	}, nil
}

func (a *auth) GenVerifyEmailJWT(ctx context.Context, id string, req UpdateEmailRequest) (string, error) {
	claims := Claims{
		AID:   id,
		Type:  VerifyEmailToken,
		Email: &req.Email,
	}

	token, err := jwt.NewJWT(makeClaims(claims, _verifyEmailTokenExp), a.env.JWTSecretKey)
	if err != nil {
		return "", errors.Wrap(err, "new verify email token")
	}

	return token.String, nil
}

func (a *auth) VerifyEmailToken(ctx context.Context, token string) (*Auth, error) {
	claims, err := _validateJWT(token, a.env.JWTSecretKey)
	if err != nil {
		return nil, errors.Wrap(err, "validate token")
	}

	var tokenClaims Claims
	if err := claims.DecodeClaims(&tokenClaims); err != nil {
		return nil, errors.Wrap(err, "parse claims")
	}

	if tokenClaims.Type != VerifyEmailToken {
		return nil, errors.New("invalid token type")
	}

	if tokenClaims.Email == nil {
		return nil, errors.New("email is required")
	}

	return &Auth{
		ID:       tokenClaims.AID,
		Username: pointer.Safe(tokenClaims.Email),
	}, nil
}

func (a *auth) DeleteJWT(ctx context.Context, token string) error {
	if err := a.r.DeleteRefreshToken(ctx, token); err != nil {
		return errors.Wrap(err, "delete refresh token")
	}

	return nil
}

func makeSignupClaims(claims SignupClaims, exp time.Duration) SignupClaims {
	now := time.Now()

	if exp > 0 {
		claims.ExpiresAt = now.Add(exp).Unix()
	}
	claims.IssuedAt = now.Unix()
	claims.NotBefore = now.Unix()

	return claims
}

func makeClaims(claims Claims, exp time.Duration) Claims {
	now := time.Now()

	if exp > 0 {
		claims.ExpiresAt = now.Add(exp).Unix()
	}
	claims.IssuedAt = now.Unix()
	claims.NotBefore = now.Unix()

	return claims
}
