package auth

import (
	"log"
	"time"

	"as-api/as/foundations/env"
	"as-api/as/internal/auth/repository"
	"as-api/as/pkg/context"
	"as-api/as/pkg/di"
	"as-api/as/pkg/jwt"

	"github.com/pkg/errors"
	"golang.org/x/crypto/bcrypt"
)

func init() {
	if err := di.RegisterProviders(New); err != nil {
		log.Fatal("register constructor auth domain failed:", err)
	}
}

const (
	_accessTokenExp        = 2 * time.Hour
	_refreshTokenExp       = 7 * 24 * time.Hour
	_resetPasswordTokenExp = 15 * time.Minute
	_signupTokenExp        = 24 * time.Hour
	_verifyEmailTokenExp   = 24 * time.Hour
)

type TokenType = jwt.TokenType

const (
	AccessToken        TokenType = "accessToken"
	RefreshToken       TokenType = "refreshToken"
	ResetPasswordToken TokenType = "resetPasswordToken"
	SignupToken        TokenType = "signupToken"
	VerifyEmailToken   TokenType = "verifyEmailToken"
)

type Claims = jwt.CustomClaims

type SignupClaims struct {
	Email             *string   `json:"email"`
	Type              TokenType `json:"type"`
	RegisterType      *string   `json:"register_type,omitempty"`
	Lang              *string   `json:"lang"`
	CountryCode       *string   `json:"country_code"`
	RegionId          *string   `json:"region_id,omitempty"`
	ReceiveNewsletter bool      `json:"receive_newsletter"`

	jwt.DefaultClaims
}

type AuthToken struct {
	AccessToken  string
	RefreshToken string
}

type Auth struct {
	ID        string
	UserID    *string
	AdminID   *string
	Username  string
	AccountID string
}

type SignupRequest struct {
	Email             string
	Lang              *string
	ReceiveNewsletter bool
	CountryCode       *string
	RegionId          *string
	Type              *string
}

type SignUpToken struct {
	ID        *string
	Email     string
	AccountID string
}

type UpdateEmailRequest struct {
	Email string
}

type VerifyEmailRequest struct {
	Token string
}

type AuthDomain interface {
	FindOneByID(ctx context.Context, id string) (*Auth, error)
	GenJWT(ctx context.Context, claims Claims) (*AuthToken, error)
	GenResetPasswordJWT(ctx context.Context, authID string) (string, error)
	GenSignupJWT(ctx context.Context, req SignupRequest) (string, error)
	PasswordAuthen(ctx context.Context, username, password string) (*Auth, error)
	TokenAuthen(ctx context.Context, token string) (*Auth, error)
	FindAuthByUserID(ctx context.Context, userID string) (*Auth, error)
	FindAuthByUsername(ctx context.Context, username string) (*Auth, error)
	VerifyResetPasswordToken(ctx context.Context, token string) (*Auth, error)
	VerifySignupToken(ctx context.Context, token string) (*SignUpToken, error)
	DeleteJWT(ctx context.Context, token string) error
	SetPassword(ctx context.Context, authID string, password string) error
	AttachUserID(ctx context.Context, authID, userID, password string) error
	GenVerifyEmailJWT(ctx context.Context, id string, req UpdateEmailRequest) (string, error)
	VerifyEmailToken(ctx context.Context, token string) (*Auth, error)
	UpdateUsername(ctx context.Context, id string, username string) error
	DeleteAuthByUserID(ctx context.Context, userID string) error
}

type auth struct {
	r   repository.Repository
	env env.MapperData
}

func New(r repository.Repository, env env.MapperData) AuthDomain {
	return &auth{r: r, env: env}
}

func (a *auth) FindOneByID(ctx context.Context, id string) (*Auth, error) {
	au, err := a.r.FindOneByID(ctx, id)
	if err != nil {
		return nil, errors.Wrap(err, "find auth by ID")
	}

	return ParseAuthFromEntities(au), nil
}

func (a *auth) FindAuthByUserID(ctx context.Context, userID string) (*Auth, error) {
	au, err := a.r.FindOneByUserID(ctx, userID)
	if err != nil {
		return nil, errors.Wrap(err, "find auth by user ID")
	}

	return ParseAuthFromEntities(au), nil
}

func (a *auth) FindAuthByUsername(ctx context.Context, username string) (*Auth, error) {
	au, err := a.r.FindOneByUsername(ctx, username)
	if err != nil {
		return nil, errors.Wrap(err, "find auth by username")
	}

	return ParseAuthFromEntities(au), nil
}

func (a *auth) AttachUserID(ctx context.Context, authID, userID, password string) error {
	if authID == "" || userID == "" {
		return errors.New("auth ID and user ID are required")
	}

	// Hash password with bcrypt
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return errors.Wrap(err, "hash password")
	}

	if err := a.r.AttachUserID(ctx, authID, userID, string(hashedPassword)); err != nil {
		return errors.Wrap(err, "attach user ID")
	}

	return nil
}

func (a *auth) UpdateUsername(ctx context.Context, id string, username string) error {
	if err := a.r.UpdateUsername(ctx, id, username); err != nil {
		return errors.Wrap(err, "update auth")
	}

	return nil
}

// DeleteAuthByUserID implements AuthDomain.
func (a *auth) DeleteAuthByUserID(ctx context.Context, userID string) error {
	if err := a.r.DeleteAuthByUserID(ctx, userID); err != nil {
		return errors.Wrap(err, "delete auth by user ID")
	}

	return nil
}
