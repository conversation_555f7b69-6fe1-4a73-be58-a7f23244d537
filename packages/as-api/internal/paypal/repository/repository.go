package repository

import (
	"context"

	"as-api/as/foundations/db/entities"
	"as-api/as/foundations/db/factory"
	"as-api/as/pkg/helpers/apiutil"

	"github.com/pkg/errors"
	"gorm.io/gorm"
)

type Repository interface {
	CreateVaultToken(ctx context.Context, paypalUser *entities.PaypalUser) (*entities.PaypalUser, error)
	FindByCustomerID(ctx context.Context, customerID string) (*entities.PaypalUser, error)
	FindByVaultCardID(ctx context.Context, vaultCardID string) (*entities.PaypalUser, error)
	UpdateVaultToken(ctx context.Context, id string, paypalUser *entities.PaypalUser) (*entities.PaypalUser, error)
	DeleteVaultToken(ctx context.Context, id string) error
}

type repository struct {
	db *factory.DO
}

func NewRepository(db *factory.DO) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) CreateVaultToken(ctx context.Context, paypalUser *entities.PaypalUser) (*entities.PaypalUser, error) {
	db := r.db.Model(nil)

	if err := db.Query.PaypalUser.WithContext(ctx).Create(paypalUser); err != nil {
		return nil, errors.Wrap(err, "create paypal user vault token")
	}

	return paypalUser, nil
}

func (r *repository) FindByCustomerID(ctx context.Context, customerID string) (*entities.PaypalUser, error) {
	db := r.db.Model(nil)

	p := db.Query.PaypalUser

	q := p.WithContext(ctx).Where(p.CustomerID.Eq(customerID))

	paypalUser, err := q.First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, apiutil.ErrResourceNotFound
		}
		return nil, errors.Wrap(err, "find paypal user by customer id")
	}

	return paypalUser, nil
}

func (r *repository) FindByVaultCardID(ctx context.Context, vaultCardID string) (*entities.PaypalUser, error) {
	db := r.db.Model(nil)

	p := db.Query.PaypalUser

	q := p.WithContext(ctx).Where(p.VaultCardID.Eq(vaultCardID))

	paypalUser, err := q.First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, apiutil.ErrResourceNotFound
		}
		return nil, errors.Wrap(err, "find paypal user by vault card id")
	}

	return paypalUser, nil
}

func (r *repository) UpdateVaultToken(ctx context.Context, id string, paypalUser *entities.PaypalUser) (*entities.PaypalUser, error) {
	db := r.db.Model(nil)

	p := db.Query.PaypalUser

	_, err := p.WithContext(ctx).Where(p.ID.Eq(id)).Updates(paypalUser)
	if err != nil {
		return nil, errors.Wrap(err, "update paypal user vault token")
	}

	// Fetch the updated record
	updated, err := p.WithContext(ctx).Where(p.ID.Eq(id)).First()
	if err != nil {
		return nil, errors.Wrap(err, "fetch updated paypal user")
	}

	return updated, nil
}

func (r *repository) DeleteVaultToken(ctx context.Context, id string) error {
	db := r.db.Model(nil)

	p := db.Query.PaypalUser

	_, err := p.WithContext(ctx).Where(p.ID.Eq(id)).Delete()
	if err != nil {
		return errors.Wrap(err, "delete paypal user vault token")
	}

	return nil
}
