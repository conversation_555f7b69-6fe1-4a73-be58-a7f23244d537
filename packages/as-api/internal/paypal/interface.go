package paypal

import (
	"context"

	"as-api/as/foundations/db/entities"
	"as-api/as/foundations/paypalapi"
)

// VaultTokenData represents the data to be stored for a vault payment token
type VaultTokenData struct {
	CustomerID  string
	VaultCardID string
	CardInfo    *CardInfo
}

// CardInfo represents the card information from PayPal response
type CardInfo struct {
	Brand      *string `json:"brand,omitempty"`
	LastDigits *string `json:"last_digits,omitempty"`
	Name       *string `json:"name,omitempty"`
	Expiry     *string `json:"expiry,omitempty"`
}

type PayPalDomain interface {
	StoreVaultToken(ctx context.Context, customerID string, paypalResp *paypalapi.CreateVaultPaymentTokenResponse) (*entities.PaypalUser, error)
	GetVaultTokenByCustomerID(ctx context.Context, customerID string) (*entities.PaypalUser, error)
	GetVaultTokenByVaultCardID(ctx context.Context, vaultCardID string) (*entities.PaypalUser, error)
}
