package paypal

import (
	"context"

	"as-api/as/foundations/db/entities"
	"as-api/as/foundations/logger"
	"as-api/as/foundations/paypalapi"
	"as-api/as/internal/paypal/repository"
	"as-api/as/pkg/helpers/pointer"

	"github.com/pkg/errors"
)

type domain struct {
	repo repository.Repository
	log  logger.Logger
}

func NewPayPalDomain(repo repository.Repository, log logger.Logger) PayPalDomain {
	return &domain{
		repo: repo,
		log:  log,
	}
}

// convertCardBrandToString converts PayPal CardBrand to string pointer
func convertCardBrandToString(brand interface{}) *string {
	if brand == nil {
		return nil
	}
	brandStr := string(brand.(string))
	return &brandStr
}

func (d *domain) StoreVaultToken(ctx context.Context, customerID string, paypalResp *paypalapi.CreateVaultPaymentTokenResponse) (*entities.PaypalUser, error) {
	if customerID == "" {
		return nil, errors.New("customer ID is empty")
	}

	if paypalResp == nil {
		return nil, errors.New("paypal response is nil")
	}

	if paypalResp.ID == "" {
		return nil, errors.New("vault card ID is empty")
	}

	// Check if vault token already exists for this customer
	existing, err := d.repo.FindByCustomerID(ctx, customerID)
	if err == nil && existing != nil {
		d.log.Warn("vault token already exists for customer", logger.FieldMap{
			"customer_id":            customerID,
			"existing_vault_card_id": existing.VaultCardID,
			"new_vault_card_id":      paypalResp.ID,
		})
		// For now, we'll create a new record. In the future, you might want to update the existing one
		// or implement a policy for handling multiple vault tokens per customer
	}

	// Extract card information from PayPal response
	var cardInfo *entities.JSON
	if paypalResp.PaymentSource.Card != nil {
		cardInfoData := &CardInfo{
			Brand:      convertCardBrandToString(paypalResp.PaymentSource.Card.Brand),
			LastDigits: paypalResp.PaymentSource.Card.LastDigits,
			Name:       paypalResp.PaymentSource.Card.Name,
		}

		// Convert expiry to string if available
		if paypalResp.PaymentSource.Card.Expiry != nil {
			cardInfoData.Expiry = pointer.Ptr(string(*paypalResp.PaymentSource.Card.Expiry))
		}

		// Convert to JSON
		cardInfoJSON, err := entities.ConvertDataToJSON(cardInfoData)
		if err != nil {
			d.log.Error("failed to marshal card info", logger.FieldMap{
				"error": err,
			})
			// Continue without card info rather than failing
			cardInfo = nil
		} else {
			cardInfo = cardInfoJSON
		}
	}

	// Create PaypalUser entity
	paypalUser := &entities.PaypalUser{
		CustomerID:  customerID,
		VaultCardID: paypalResp.ID,
		CardInfo:    cardInfo,
	}

	// Store in database
	result, err := d.repo.CreateVaultToken(ctx, paypalUser)
	if err != nil {
		d.log.Error("failed to store vault token", logger.FieldMap{
			"customer_id":   customerID,
			"vault_card_id": paypalResp.ID,
			"error":         err,
		})
		return nil, errors.Wrap(err, "failed to store vault token")
	}

	d.log.Info("vault token stored successfully", logger.FieldMap{
		"customer_id":   customerID,
		"vault_card_id": paypalResp.ID,
	})

	return result, nil
}

func (d *domain) GetVaultTokenByCustomerID(ctx context.Context, customerID string) (*entities.PaypalUser, error) {
	if customerID == "" {
		return nil, errors.New("customer ID is empty")
	}

	result, err := d.repo.FindByCustomerID(ctx, customerID)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get vault token by customer ID")
	}

	return result, nil
}

func (d *domain) GetVaultTokenByVaultCardID(ctx context.Context, vaultCardID string) (*entities.PaypalUser, error) {
	if vaultCardID == "" {
		return nil, errors.New("vault card ID is empty")
	}

	result, err := d.repo.FindByVaultCardID(ctx, vaultCardID)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get vault token by vault card ID")
	}

	return result, nil
}
