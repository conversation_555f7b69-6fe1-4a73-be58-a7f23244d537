package deeplink

import (
	"context"
	"log"

	"as-api/as/pkg/di"
)

func init() {
	if err := di.RegisterProviders(NewDeepLinkDomain); err != nil {
		log.Fatal("register constructor deeplink domain failed:", err)
	}
}

// DeepLinkType represents the type of deep link to generate
type DeepLinkType string

const (
	DeepLinkTypeNotice           DeepLinkType = "notice"
	DeepLinkTypeThread           DeepLinkType = "thread"
	DeepLinkTypeProduct          DeepLinkType = "product"
	DeepLinkTypePurchasedItem    DeepLinkType = "purchased_item"
	DeepLinkTypeSoldItem         DeepLinkType = "sold_item"
	DeepLinkTypeVaultCardSuccess DeepLinkType = "vault_card_success"
	DeepLinkTypeVaultCardFailure DeepLinkType = "vault_card_failure"
)

// DeepLinkRequest represents a request to generate a deep link
type DeepLinkRequest struct {
	Type DeepLinkType
	ID   string
	Data map[string]string // Additional data from notification
}

// DeepLinkResponse represents the response from deep link generation
type DeepLinkResponse struct {
	Path string // The relative path (e.g., "/products/123")
	URL  string // The full URL with host (e.g., "https://app.example.com/products/123")
}

// DeepLinkDomain defines the interface for deep link operations
type DeepLinkDomain interface {
	// GenerateDeepLink generates a deep link based on the request
	GenerateDeepLink(ctx context.Context, req *DeepLinkRequest) (*DeepLinkResponse, error)

	// GenerateDeepLinkFromNotification generates a deep link from notification data
	// This is a convenience method that maps notification types to deep link requests
	GenerateDeepLinkFromNotification(ctx context.Context, systemType, notificationType string, notificationID string, data map[string]string) (*DeepLinkResponse, error)

	// ValidateDeepLinkRequest validates that a deep link request has all required data
	ValidateDeepLinkRequest(req *DeepLinkRequest) error
}

// Implementation is in deeplink.go
