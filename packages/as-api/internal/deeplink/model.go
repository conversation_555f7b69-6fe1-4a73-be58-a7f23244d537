package deeplink

import (
	"fmt"

	dtos "as-api/as/dtos/user"
)

// DeepLinkError represents errors that can occur during deep link generation
type DeepLinkError struct {
	Type    DeepLinkType
	Message string
	Cause   error
}

func (e *DeepLinkError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("deeplink error for type %s: %s (caused by: %v)", e.Type, e.Message, e.Cause)
	}
	return fmt.Sprintf("deeplink error for type %s: %s", e.Type, e.Message)
}

// NewDeepLinkError creates a new deep link error
func NewDeepLinkError(linkType DeepLinkType, message string, cause error) *DeepLinkError {
	return &DeepLinkError{
		Type:    linkType,
		Message: message,
		Cause:   cause,
	}
}

// NotificationTypeMapping maps notification types to deep link types
var NotificationTypeMapping = map[dtos.NotificationType]DeepLinkType{
	dtos.INCOMINGMESSAGE: DeepLinkTypeThread,

	// Product-related notifications
	dtos.ITEMONSALE:       DeepLinkTypeProduct,
	dtos.ITEMREMOVED:      DeepLinkTypeProduct,
	dtos.PRODUCTRECEIVED:  DeepLinkTypeProduct,
	dtos.FAVONSALE:        DeepLinkTypeProduct,
	dtos.CARTPURCHASED:    DeepLinkTypeProduct,
	dtos.PRICECHANGED:     DeepLinkTypeProduct,
	dtos.PRODUCTFAVORITED: DeepLinkTypeProduct,

	// Purchase tracking notifications
	dtos.TXNSHIPPED:   DeepLinkTypePurchasedItem,
	dtos.TXNPREPARING: DeepLinkTypePurchasedItem,

	// Sales tracking notifications
	dtos.PRODUCTPURCHASED: DeepLinkTypeSoldItem,
}

// DataKeyMapping maps deep link types to their required data keys
var DataKeyMapping = map[DeepLinkType]string{
	DeepLinkTypeThread:        "thread_id",
	DeepLinkTypeProduct:       "product_id",
	DeepLinkTypePurchasedItem: "item_id",
	DeepLinkTypeSoldItem:      "item_id",
}

// PathTemplateMapping maps deep link types to their URL path templates
var PathTemplateMapping = map[DeepLinkType]string{
	DeepLinkTypeNotice:           "/notices/%s",
	DeepLinkTypeThread:           "/threads/%s",
	DeepLinkTypeProduct:          "/products/%s",
	DeepLinkTypePurchasedItem:    "/purchased-items/%s",
	DeepLinkTypeSoldItem:         "/sold-items/%s",
	DeepLinkTypeVaultCardSuccess: "/vault-cards/%s/success",
	DeepLinkTypeVaultCardFailure: "/vault-cards/%s/failure",
}

// GetRequiredDataKey returns the required data key for a given deep link type
func GetRequiredDataKey(linkType DeepLinkType) (string, bool) {
	key, exists := DataKeyMapping[linkType]
	return key, exists
}

// GetPathTemplate returns the path template for a given deep link type
func GetPathTemplate(linkType DeepLinkType) (string, bool) {
	template, exists := PathTemplateMapping[linkType]
	return template, exists
}

// MapNotificationTypeToDeepLinkType maps a notification type to a deep link type
func MapNotificationTypeToDeepLinkType(notificationType dtos.NotificationType) (DeepLinkType, bool) {
	linkType, exists := NotificationTypeMapping[notificationType]
	return linkType, exists
}
