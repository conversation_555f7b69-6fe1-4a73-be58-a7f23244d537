package service

import (
	"context"

	"as-api/as/api/admin/auth/domain"
	dtos "as-api/as/dtos/admin"
	"as-api/as/internal/admin"
	"as-api/as/internal/auth"
	"as-api/as/internal/user"
	"as-api/as/pkg/helpers/apiutil"
	"as-api/as/pkg/validator"

	"github.com/pkg/errors"
)

type svc struct {
	auth  auth.AuthDomain
	user  user.UserDomain
	admin admin.AdminDomain
	v     validator.Validator
}

// Auth implements domain.AuthService.
func (s *svc) Auth(ctx context.Context, req dtos.AuthRequest) (*dtos.AuthResponse, error) {
	if err := s.v.Validate(req); err != nil {
		return nil, errors.Wrapf(apiutil.ErrInvalidRequest, "validate: %v", err)
	}

	au, err := s.authen(ctx, req)
	if err != nil {
		return nil, errors.Wrap(err, "authen")
	}

	if au == nil || au.AdminID == nil {
		return nil, errors.Wrap(apiutil.ErrResourceNotFound, "not found")
	}

	adm, err := s.admin.ReadOne(ctx, *au.AdminID)
	if err != nil {
		return nil, errors.Wrap(err, "get admin")
	}

	token, err := s.auth.GenJWT(ctx, auth.Claims{
		AID:     au.ID,
		UID:     *au.AdminID,
		Name:    adm.Name,
		CID:     au.AccountID,
		IsAdmin: true,
	})
	if err != nil {
		return nil, errors.Wrap(err, "gen jwt")
	}

	return &dtos.AuthResponse{
		AccessToken:  token.AccessToken,
		RefreshToken: token.RefreshToken,
	}, nil
}

func (s *svc) authen(ctx context.Context, req dtos.AuthRequest) (*auth.Auth, error) {
	switch req.GrantType {
	case dtos.Password:
		if req.Username == nil || req.Password == nil {
			return nil, errors.Wrapf(apiutil.ErrInvalidRequest, "username or password is nil")
		}

		return s.auth.PasswordAuthen(ctx, *req.Username, *req.Password)

	case dtos.RefreshToken:
		if req.RefreshToken == nil {
			return nil, errors.Wrapf(apiutil.ErrInvalidRequest, "refresh token is nil")
		}

		return s.auth.TokenAuthen(ctx, *req.RefreshToken)

	default:
		return nil, errors.Wrap(apiutil.ErrInvalidRequest, "grant type invalid")
	}
}

func NewService(auth auth.AuthDomain, user user.UserDomain, adm admin.AdminDomain, v validator.Validator) domain.AuthService {
	return &svc{
		auth:  auth,
		user:  user,
		admin: adm,
		v:     v,
	}
}
