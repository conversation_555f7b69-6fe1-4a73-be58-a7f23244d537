package service

import (
	"as-api/as/api/user/payment/domain"
	dtos "as-api/as/dtos/user"
	"as-api/as/foundations/paypalapi"
	"as-api/as/internal/auth"
	"as-api/as/internal/deeplink"
	"as-api/as/internal/payment"
	"as-api/as/internal/paypal"
	"as-api/as/internal/user"
	"as-api/as/pkg/context"
	"as-api/as/pkg/helpers/apiutil"
	"as-api/as/pkg/helpers/pointer"
	"as-api/as/pkg/validator"

	"github.com/pkg/errors"
)

type svc struct {
	payment      payment.PaymentDomain
	user         user.UserDomain
	auth         auth.AuthDomain
	paypalClient paypalapi.PayPalClient
	paypalDomain paypal.PayPalDomain
	deeplink     deeplink.DeepLinkDomain

	v validator.Validator
}

func NewPaymentService(payment payment.PaymentDomain, user user.UserDomain, auth auth.AuthDomain, deeplink deeplink.DeepLinkDomain, paypalClient paypalapi.PayPalClient, paypalDomain paypal.PayPalDomain, v validator.Validator) domain.PaymentService {
	return &svc{
		payment:      payment,
		user:         user,
		auth:         auth,
		deeplink:     deeplink,
		paypalClient: paypalClient,
		paypalDomain: paypalDomain,
		v:            v,
	}
}

func (s *svc) VerifyCreditCard(ctx context.Context, req *dtos.VerifyCreditCardRequest) (*dtos.VerifyCreditCardResponse, error) {
	if err := s.v.Validate(req); err != nil {
		return nil, errors.Wrapf(apiutil.ErrInvalidRequest, "validate request: %v", err)
	}

	aid, err := context.GetAuthID(ctx)
	if err != nil {
		return nil, errors.Wrapf(apiutil.ErrPermissionDenied, "get auth id: %v", err)
	}

	auth, err := s.auth.FindOneByID(ctx, aid)
	if err != nil {
		return nil, errors.Wrap(err, "get auth")
	}

	payer := payment.Payer{
		Email: auth.Username,
	}

	if req.Address != nil {
		payer.Name = pointer.Safe(req.Address.FullName)
		if req.Address.Phone != nil {
			payer.HomePhone = pointer.Safe(convertDTOPhoneToDomainPaymentPhone(req.Address.Phone))
		}
		if req.Address.Mobile != nil {
			payer.MobilePhone = convertDTOPhoneToDomainPaymentPhone(req.Address.Mobile)
		}
	}

	if auth.UserID != nil {
		user, err := s.user.ReadOne(ctx, *auth.UserID)
		if err != nil {
			return nil, errors.Wrap(err, "get user")
		}

		if user == nil {
			return nil, errors.Wrap(apiutil.ErrResourceNotFound, "user not found")
		}

		payerDomain, err := getDomainPayerFromDomainUser(user)
		if err != nil {
			return nil, errors.Wrap(err, "get payer")
		}

		payer = pointer.Safe(payerDomain)
	}

	verifyCardReq := &payment.VerifyCardRequest{
		Token: req.Token,
		Payer: payer,
	}

	res, err := s.payment.VerifyCard(ctx, verifyCardReq)
	if err != nil {
		return nil, errors.Wrap(err, "verify card")
	}

	return &dtos.VerifyCreditCardResponse{
		AccessId:    res.AccessID,
		RedirectUrl: res.RedirectURL,
	}, nil
}

func (s *svc) CreateVaultSetupToken(ctx context.Context, req *dtos.CreateVaultSetupTokenRequest) (*dtos.CreateVaultSetupTokenResponse, error) {
	if err := s.v.Validate(req); err != nil {
		return nil, errors.Wrapf(apiutil.ErrInvalidRequest, "validate request: %v", err)
	}

	cid, err := context.GetCustomerID(ctx)
	if err != nil {
		return nil, errors.Wrapf(apiutil.ErrPermissionDenied, "get auth id: %v", err)
	}

	returnUrl, err := s.deeplink.GenerateDeepLink(ctx, &deeplink.DeepLinkRequest{
		Type: deeplink.DeepLinkTypeVaultCardSuccess,
		ID:   cid,
	})
	if err != nil {
		return nil, errors.Wrap(err, "generate deep link")
	}

	cancelUrl, err := s.deeplink.GenerateDeepLink(ctx, &deeplink.DeepLinkRequest{
		Type: deeplink.DeepLinkTypeVaultCardFailure,
		ID:   cid,
	})
	if err != nil {
		return nil, errors.Wrap(err, "generate deep link")
	}

	// Convert DTO to PayPal API request
	paypalReq := &paypalapi.CreateVaultSetupTokenRequest{
		Customer: &paypalapi.Customer{
			ID: cid,
		},
		PaymentSource: paypalapi.PaymentSourceRequest{
			Card: &paypalapi.CardRequest{},
		},
		ExperienceContext: &paypalapi.ExperienceContext{
			ReturnUrl: pointer.Ptr(returnUrl.URL),
			CancelUrl: pointer.Ptr(cancelUrl.URL),
		},
	}

	if req.PaymentSource.Card != nil {
		paypalReq.PaymentSource.Card.BillingAddress = convertToPayPalBillingAddress(req.PaymentSource.Card.BillingAddress)
	}

	// Call PayPal API
	paypalResp, err := s.paypalClient.CreateVaultSetupToken(ctx, paypalReq)
	if err != nil {
		return nil, errors.Wrap(err, "create vault setup token")
	}

	return pointer.Ptr(dtos.CreateVaultSetupTokenResponse(paypalResp)), nil
}

func (s *svc) CreateVaultPaymentToken(ctx context.Context, req *dtos.CreateVaultPaymentTokenRequest) (*dtos.CreateVaultPaymentTokenResponse, error) {
	if err := s.v.Validate(req); err != nil {
		return nil, errors.Wrapf(apiutil.ErrInvalidRequest, "validate request: %v", err)
	}

	// Get customer ID from context
	cid, err := context.GetCustomerID(ctx)
	if err != nil {
		return nil, errors.Wrapf(apiutil.ErrPermissionDenied, "get customer id: %v", err)
	}

	// Convert DTO to PayPal API request
	paypalReq := &paypalapi.CreateVaultPaymentTokenRequest{
		PaymentSource: paypalapi.PaymentTokenSource{
			Token: paypalapi.Token{
				ID:   req.VaultSetupToken,
				Type: paypalapi.TokenTypeSetupToken,
			},
		},
	}

	// Call PayPal API
	paypalResp, err := s.paypalClient.CreateVaultPaymentToken(ctx, paypalReq)
	if err != nil {
		return nil, errors.Wrap(err, "create vault payment token")
	}

	// Store vault token in database
	_, err = s.paypalDomain.StoreVaultToken(ctx, cid, paypalResp)
	if err != nil {
		// Since PayPal token creation succeeded, we should return the token to the client
		// but also return an error to indicate the storage issue
		// This allows the client to proceed while alerting about the storage problem
		return nil, errors.Wrap(err, "vault token created successfully but failed to store in database")
	}

	return pointer.Ptr(dtos.CreateVaultPaymentTokenResponse(paypalResp)), nil
}
