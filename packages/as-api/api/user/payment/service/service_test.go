package service

import (
	"context"
	"errors"
	"testing"

	"as-api/as/api/user/payment/domain"
	dtos "as-api/as/dtos/user"
	"as-api/as/foundations/paypalapi"
	"as-api/as/internal/auth"
	"as-api/as/internal/payment"
	"as-api/as/internal/user"
	authmock "as-api/as/mocks/domains/auth"
	paymentmock "as-api/as/mocks/domains/payment"
	usermock "as-api/as/mocks/domains/user"
	validatormock "as-api/as/mocks/validator"
	pkgcontext "as-api/as/pkg/context"
	"as-api/as/pkg/helpers/pointer"
	"as-api/as/pkg/jwt"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// Mock PayPal client for testing
type mockPayPalClient struct {
	mock.Mock
}

// Mock DeepLink domain for testing
type mockDeepLinkDomain struct {
	mock.Mock
}

func (m *mockDeepLinkDomain) GenerateDeepLink(ctx context.Context, req interface{}) (interface{}, error) {
	args := m.Called(ctx, req)
	return args.Get(0), args.Error(1)
}

// Mock PayPal domain for testing
type mockPayPalDomain struct {
	mock.Mock
}

func (m *mockPayPalDomain) StoreVaultToken(ctx context.Context, customerID string, paypalResp interface{}) (interface{}, error) {
	args := m.Called(ctx, customerID, paypalResp)
	return args.Get(0), args.Error(1)
}

func (m *mockPayPalDomain) GetVaultTokenByCustomerID(ctx context.Context, customerID string) (interface{}, error) {
	args := m.Called(ctx, customerID)
	return args.Get(0), args.Error(1)
}

func (m *mockPayPalDomain) GetVaultTokenByVaultCardID(ctx context.Context, vaultCardID string) (interface{}, error) {
	args := m.Called(ctx, vaultCardID)
	return args.Get(0), args.Error(1)
}

func (m *mockPayPalClient) CreateVaultSetupToken(ctx context.Context, req *paypalapi.CreateVaultSetupTokenRequest) (*paypalapi.CreateVaultSetupTokenResponse, error) {
	args := m.Called(ctx, req)
	return args.Get(0).(*paypalapi.CreateVaultSetupTokenResponse), args.Error(1)
}

func (m *mockPayPalClient) CreateVaultPaymentToken(ctx context.Context, req *paypalapi.CreateVaultPaymentTokenRequest) (*paypalapi.CreateVaultPaymentTokenResponse, error) {
	args := m.Called(ctx, req)
	return args.Get(0).(*paypalapi.CreateVaultPaymentTokenResponse), args.Error(1)
}

func TestVerifyCreditCard(t *testing.T) {
	testAuthID := "auth-123"
	testUserID := "user-123"
	testToken := "test-token"

	tests := []struct {
		name          string
		setupMock     func(paymentDomain *paymentmock.PaymentDomain, userDomain *usermock.UserDomain, authDomain *authmock.AuthDomain, validator *validatormock.Validator, ctx context.Context) context.Context
		request       *dtos.VerifyCreditCardRequest
		expected      *dtos.VerifyCreditCardResponse
		expectedError string
	}{
		{
			name: "Success - Verify credit card without user",
			setupMock: func(paymentDomain *paymentmock.PaymentDomain, userDomain *usermock.UserDomain, authDomain *authmock.AuthDomain, validator *validatormock.Validator, ctx context.Context) context.Context {
				// Set claims in context
				claims := jwt.CustomClaims{
					AID: testAuthID,
				}
				ctx = context.WithValue(ctx, pkgcontext.ClaimsCtx, claims)

				// Mock validator
				validator.On("Validate", mock.Anything).Return(nil)

				// Mock auth domain
				authDomain.On("FindOneByID", mock.Anything, testAuthID).Return(&auth.Auth{
					ID:       testAuthID,
					Username: "<EMAIL>",
					UserID:   nil,
				}, nil)

				// Mock payment domain
				paymentDomain.On("VerifyCard", mock.Anything, mock.MatchedBy(func(req *payment.VerifyCardRequest) bool {
					return req.Token == testToken &&
						req.Payer.Email == "<EMAIL>"
				})).Return(&payment.VerifyCardResponse{
					AccessID:    pointer.Ptr("access-123"),
					RedirectURL: pointer.Ptr("https://redirect.url"),
				}, nil)

				return ctx
			},
			request: &dtos.VerifyCreditCardRequest{
				Token: testToken,
			},
			expected: &dtos.VerifyCreditCardResponse{
				AccessId:    pointer.Ptr("access-123"),
				RedirectUrl: pointer.Ptr("https://redirect.url"),
			},
			expectedError: "",
		},
		{
			name: "Success - Verify credit card with user and address",
			setupMock: func(paymentDomain *paymentmock.PaymentDomain, userDomain *usermock.UserDomain, authDomain *authmock.AuthDomain, validator *validatormock.Validator, ctx context.Context) context.Context {
				// Set claims in context
				claims := jwt.CustomClaims{
					AID: testAuthID,
				}
				ctx = context.WithValue(ctx, pkgcontext.ClaimsCtx, claims)

				// Mock validator
				validator.On("Validate", mock.Anything).Return(nil)

				// Mock auth domain
				authDomain.On("FindOneByID", mock.Anything, testAuthID).Return(&auth.Auth{
					ID:       testAuthID,
					Username: "<EMAIL>",
					UserID:   &testUserID,
				}, nil)

				// Mock user domain
				userDomain.On("ReadOne", mock.Anything, testUserID).Return(&user.User{
					ID:        pointer.Ptr(testUserID),
					Email:     "<EMAIL>",
					FirstName: pointer.Ptr("John"),
					LastName:  pointer.Ptr("Doe"),
					CreditCard: &user.CreditCard{
						CardID: pointer.Ptr("card-123"),
					},
					HomeAddress: &user.Address{},
				}, nil)

				// Mock payment domain
				paymentDomain.On("VerifyCard", mock.Anything, mock.MatchedBy(func(req *payment.VerifyCardRequest) bool {
					return req.Token == testToken &&
						req.Payer.Email == "<EMAIL>" &&
						req.Payer.Name == "John Doe"
				})).Return(&payment.VerifyCardResponse{
					AccessID:    pointer.Ptr("access-123"),
					RedirectURL: pointer.Ptr("https://redirect.url"),
				}, nil)

				return ctx
			},
			request: &dtos.VerifyCreditCardRequest{
				Token: testToken,
				Address: &dtos.Address{
					FullName: pointer.Ptr("Test User"),
					Phone: &dtos.Phone{
						CountryCode: "+1",
						Number:      "1234567890",
					},
					Mobile: &dtos.Phone{
						CountryCode: "+1",
						Number:      "0987654321",
					},
				},
			},
			expected: &dtos.VerifyCreditCardResponse{
				AccessId:    pointer.Ptr("access-123"),
				RedirectUrl: pointer.Ptr("https://redirect.url"),
			},
			expectedError: "",
		},
		{
			name: "Error - Validation fails",
			setupMock: func(paymentDomain *paymentmock.PaymentDomain, userDomain *usermock.UserDomain, authDomain *authmock.AuthDomain, validator *validatormock.Validator, ctx context.Context) context.Context {
				// Mock validator returning error
				validator.On("Validate", mock.Anything).Return(errors.New("validation error"))

				return ctx
			},
			request: &dtos.VerifyCreditCardRequest{
				Token: testToken,
			},
			expected:      nil,
			expectedError: "validate request: validation error",
		},
		{
			name: "Error - Failed to get auth ID from context",
			setupMock: func(paymentDomain *paymentmock.PaymentDomain, userDomain *usermock.UserDomain, authDomain *authmock.AuthDomain, validator *validatormock.Validator, ctx context.Context) context.Context {
				// Mock validator
				validator.On("Validate", mock.Anything).Return(nil)

				// No claims in context
				return ctx
			},
			request: &dtos.VerifyCreditCardRequest{
				Token: testToken,
			},
			expected:      nil,
			expectedError: "get auth id:",
		},
		{
			name: "Error - Failed to find auth",
			setupMock: func(paymentDomain *paymentmock.PaymentDomain, userDomain *usermock.UserDomain, authDomain *authmock.AuthDomain, validator *validatormock.Validator, ctx context.Context) context.Context {
				// Set claims in context
				claims := jwt.CustomClaims{
					AID: testAuthID,
				}
				ctx = context.WithValue(ctx, pkgcontext.ClaimsCtx, claims)

				// Mock validator
				validator.On("Validate", mock.Anything).Return(nil)

				// Mock auth domain returning error
				authDomain.On("FindOneByID", mock.Anything, testAuthID).Return(nil, errors.New("auth not found"))

				return ctx
			},
			request: &dtos.VerifyCreditCardRequest{
				Token: testToken,
			},
			expected:      nil,
			expectedError: "get auth: auth not found",
		},
		{
			name: "Error - Failed to get user",
			setupMock: func(paymentDomain *paymentmock.PaymentDomain, userDomain *usermock.UserDomain, authDomain *authmock.AuthDomain, validator *validatormock.Validator, ctx context.Context) context.Context {
				// Set claims in context
				claims := jwt.CustomClaims{
					AID: testAuthID,
				}
				ctx = context.WithValue(ctx, pkgcontext.ClaimsCtx, claims)

				// Mock validator
				validator.On("Validate", mock.Anything).Return(nil)

				// Mock auth domain
				authDomain.On("FindOneByID", mock.Anything, testAuthID).Return(&auth.Auth{
					ID:       testAuthID,
					Username: "<EMAIL>",
					UserID:   &testUserID,
				}, nil)

				// Mock user domain returning error
				userDomain.On("ReadOne", mock.Anything, testUserID).Return(nil, errors.New("user not found"))

				return ctx
			},
			request: &dtos.VerifyCreditCardRequest{
				Token: testToken,
			},
			expected:      nil,
			expectedError: "get user: user not found",
		},
		{
			name: "Error - User not found",
			setupMock: func(paymentDomain *paymentmock.PaymentDomain, userDomain *usermock.UserDomain, authDomain *authmock.AuthDomain, validator *validatormock.Validator, ctx context.Context) context.Context {
				// Set claims in context
				claims := jwt.CustomClaims{
					AID: testAuthID,
				}
				ctx = context.WithValue(ctx, pkgcontext.ClaimsCtx, claims)

				// Mock validator
				validator.On("Validate", mock.Anything).Return(nil)

				// Mock auth domain
				authDomain.On("FindOneByID", mock.Anything, testAuthID).Return(&auth.Auth{
					ID:       testAuthID,
					Username: "<EMAIL>",
					UserID:   &testUserID,
				}, nil)

				// Mock user domain returning nil user
				userDomain.On("ReadOne", mock.Anything, testUserID).Return(nil, nil)

				return ctx
			},
			request: &dtos.VerifyCreditCardRequest{
				Token: testToken,
			},
			expected:      nil,
			expectedError: "user not found",
		},
		{
			name: "Error - Payment verification fails",
			setupMock: func(paymentDomain *paymentmock.PaymentDomain, userDomain *usermock.UserDomain, authDomain *authmock.AuthDomain, validator *validatormock.Validator, ctx context.Context) context.Context {
				// Set claims in context
				claims := jwt.CustomClaims{
					AID: testAuthID,
				}
				ctx = context.WithValue(ctx, pkgcontext.ClaimsCtx, claims)

				// Mock validator
				validator.On("Validate", mock.Anything).Return(nil)

				// Mock auth domain
				authDomain.On("FindOneByID", mock.Anything, testAuthID).Return(&auth.Auth{
					ID:       testAuthID,
					Username: "<EMAIL>",
					UserID:   nil,
				}, nil)

				// Mock payment domain returning error
				paymentDomain.On("VerifyCard", mock.Anything, mock.Anything).Return(nil, errors.New("payment verification failed"))

				return ctx
			},
			request: &dtos.VerifyCreditCardRequest{
				Token: testToken,
			},
			expected:      nil,
			expectedError: "verify card: payment verification failed",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mocks
			mockPaymentDomain := paymentmock.NewPaymentDomain(t)
			mockUserDomain := usermock.NewUserDomain(t)
			mockAuthDomain := authmock.NewAuthDomain(t)
			mockValidator := validatormock.NewValidator(t)

			// Setup mocks
			ctx := tt.setupMock(mockPaymentDomain, mockUserDomain, mockAuthDomain, mockValidator, context.Background())

			// Create mock PayPal client
			mockPayPalClient := &mockPayPalClient{}
			mockDeepLinkDomain := &mockDeepLinkDomain{}
			mockPayPalDomain := &mockPayPalDomain{}

			// Create service
			service := NewPaymentService(mockPaymentDomain, mockUserDomain, mockAuthDomain, mockDeepLinkDomain, mockPayPalClient, mockPayPalDomain, mockValidator)

			// Call the service
			result, err := service.VerifyCreditCard(ctx, tt.request)

			// Check error
			if tt.expectedError != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError)
			} else {
				assert.NoError(t, err)
			}

			// Check result
			if tt.expected == nil {
				assert.Nil(t, result)
			} else {
				assert.NotNil(t, result)
				assert.Equal(t, pointer.Safe(tt.expected.AccessId), pointer.Safe(result.AccessId))
				assert.Equal(t, pointer.Safe(tt.expected.RedirectUrl), pointer.Safe(result.RedirectUrl))
			}
		})
	}
}

func TestNewPaymentService(t *testing.T) {
	mockPaymentDomain := paymentmock.NewPaymentDomain(t)
	mockUserDomain := usermock.NewUserDomain(t)
	mockAuthDomain := authmock.NewAuthDomain(t)
	mockValidator := validatormock.NewValidator(t)

	// Create mock PayPal client
	mockPayPalClient := &mockPayPalClient{}

	service := NewPaymentService(mockPaymentDomain, mockUserDomain, mockAuthDomain, mockPayPalClient, mockValidator)

	assert.NotNil(t, service)
	assert.Implements(t, (*domain.PaymentService)(nil), service)
}
