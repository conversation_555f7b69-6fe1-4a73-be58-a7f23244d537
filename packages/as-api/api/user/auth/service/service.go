package service

import (
	"context"
	"fmt"

	"as-api/as/api/user/auth/domain"
	dtos "as-api/as/dtos/user"
	"as-api/as/foundations/env"
	"as-api/as/internal/auth"
	"as-api/as/internal/email"
	"as-api/as/internal/user"
	"as-api/as/pkg/helpers/apiutil"
	"as-api/as/pkg/helpers/pointer"
	"as-api/as/pkg/validator"

	"github.com/pkg/errors"
)

type svc struct {
	auth  auth.AuthDomain
	user  user.UserDomain
	email email.SMTPService
	env   env.MapperData
	v     validator.Validator
}

// Auth implements domain.AuthService.
func (s *svc) Auth(ctx context.Context, req dtos.AuthRequest) (*dtos.AuthResponse, error) {
	if err := s.v.Validate(req); err != nil {
		return nil, errors.Wrapf(apiutil.ErrInvalidRequest, "validate: %v", err)
	}

	au, err := s.authen(ctx, req)
	if err != nil {
		return nil, errors.Wrap(err, "authen")
	}

	if au == nil || au.UserID == nil {
		return nil, errors.Wrap(apiutil.ErrResourceNotFound, "not found")
	}

	user, err := s.user.ReadOne(ctx, *au.UserID)
	if err != nil {
		return nil, errors.Wrap(err, "get user")
	}

	token, err := s.auth.GenJWT(ctx, auth.Claims{
		AID:     au.ID,
		UID:     *au.UserID,
		SID:     user.SellerID,
		CID:     au.AccountID,
		Name:    user.Name,
		IsAdmin: false,
	})
	if err != nil {
		return nil, errors.Wrap(err, "gen jwt")
	}

	return &dtos.AuthResponse{
		AccessToken:  token.AccessToken,
		RefreshToken: token.RefreshToken,
	}, nil
}

func (s *svc) authen(ctx context.Context, req dtos.AuthRequest) (*auth.Auth, error) {
	switch req.GrantType {
	case dtos.Password:
		if req.Username == nil || req.Password == nil {
			return nil, errors.Wrapf(apiutil.ErrInvalidRequest, "username or password is nil")
		}

		return s.auth.PasswordAuthen(ctx, *req.Username, *req.Password)

	case dtos.RefreshToken:
		if req.RefreshToken == nil {
			return nil, errors.Wrapf(apiutil.ErrInvalidRequest, "refresh token is nil")
		}

		return s.auth.TokenAuthen(ctx, *req.RefreshToken)

	default:
		return nil, errors.Wrap(apiutil.ErrInvalidRequest, "grant type invalid")
	}
}

// RequestResetPassword implements domain.AuthService
func (s *svc) RequestResetPassword(ctx context.Context, req dtos.ResetPasswordRequestRequest) error {
	if err := s.v.Validate(req); err != nil {
		return errors.Wrapf(apiutil.ErrInvalidRequest, "validate: %v", err)
	}

	// Find auth by username
	au, err := s.auth.FindAuthByUsername(ctx, req.Email)
	if err != nil {
		return errors.Wrap(err, "find auth by username")
	}

	if au == nil || au.UserID == nil {
		return errors.Wrap(apiutil.ErrResourceNotFound, "auth not found")
	}

	// Find user by id
	user, err := s.user.ReadOne(ctx, pointer.Safe(au.UserID))
	if err != nil {
		return errors.Wrap(err, "find user by id")
	}

	// Generate reset password token
	token, err := s.auth.GenResetPasswordJWT(ctx, au.ID)
	if err != nil {
		return errors.Wrap(err, "generate reset password token")
	}

	// Create reset password link
	resetLink := fmt.Sprintf("%s/resetPassword?token=%s", s.env.ApplinkHost, token)

	// Generate email content from template
	emailContent, err := TemplateMailResetPassword(map[string]interface{}{
		"Name": pointer.Safe(user.Name),
		"Link": resetLink,
	})
	if err != nil {
		return errors.Wrap(err, "generate email content")
	}

	// Send email
	if err := s.email.Send(ctx, email.SendMailRequest{
		To:      req.Email,
		Subject: _resetPasswordSubject,
		Body:    emailContent,
	}); err != nil {
		return errors.Wrap(err, "send email")
	}

	return nil
}

// VerifyResetPasswordToken implements domain.AuthService
func (s *svc) VerifyResetPasswordToken(ctx context.Context, token string) error {
	if _, err := s.auth.VerifyResetPasswordToken(ctx, token); err != nil {
		return errors.Wrapf(apiutil.ErrInvalidToken, "verify reset password token: %v", err)
	}

	return nil
}

// ConfirmResetPassword implements domain.AuthService
func (s *svc) ConfirmResetPassword(ctx context.Context, req dtos.ResetPasswordConfirmRequest) error {
	if err := s.v.Validate(req); err != nil {
		return errors.Wrapf(apiutil.ErrInvalidRequest, "validate: %v", err)
	}

	auth, err := s.auth.VerifyResetPasswordToken(ctx, req.Token)
	if err != nil {
		return errors.Wrapf(apiutil.ErrInvalidToken, "verify reset password token: %v", err)
	}

	if err := s.auth.SetPassword(ctx, auth.ID, req.NewPassword); err != nil {
		return errors.Wrap(err, "set new password")
	}

	if err := s.auth.DeleteJWT(ctx, req.Token); err != nil {
		return errors.Wrap(err, "delete token")
	}

	return nil
}

func (s *svc) SignUp(ctx context.Context, req dtos.SignupRequest) error {
	if err := s.v.Validate(req); err != nil {
		return errors.Wrapf(apiutil.ErrInvalidRequest, "validate: %v", err)
	}

	token, err := s.auth.GenSignupJWT(ctx, auth.SignupRequest{
		Email:             req.Email,
		Lang:              req.Lang,
		ReceiveNewsletter: pointer.Safe(req.ReceiveNewsletter),
		CountryCode:       req.CountryCode,
		RegionId:          req.RegionId,
		Type:              pointer.PtrString[string](req.RegisterType),
	})
	if err != nil {
		return errors.Wrap(err, "generate signup token")
	}

	signupLink := fmt.Sprintf("%s/registerEmail?token=%s", s.env.ApplinkHost, token)

	emailContent, err := TemplateMailSignup(map[string]interface{}{
		"Link": signupLink,
	})
	if err != nil {
		return errors.Wrap(err, "generate email content")
	}

	if err := s.email.Send(ctx, email.SendMailRequest{
		To:      req.Email,
		Subject: _signupSubject,
		Body:    emailContent,
	}); err != nil {
		return errors.Wrap(err, "send email")
	}

	return nil
}

func (s *svc) VerifySignupToken(ctx context.Context, signupToken string) (*dtos.AuthResponse, error) {
	au, err := s.auth.VerifySignupToken(ctx, signupToken)
	if err != nil {
		return nil, errors.Wrapf(apiutil.ErrInvalidToken, "verify signup token: %v", err)
	}

	if au == nil || au.ID == nil {
		return nil, errors.Wrap(apiutil.ErrInvalidToken, "auth not found")
	}

	token, err := s.auth.GenJWT(ctx, auth.Claims{
		AID: pointer.Safe(au.ID),
		CID: au.AccountID,
	})
	if err != nil {
		return nil, errors.Wrap(err, "gen jwt")
	}

	return &dtos.AuthResponse{
		AccessToken:  token.AccessToken,
		RefreshToken: token.RefreshToken,
	}, nil
}

func NewService(auth auth.AuthDomain, user user.UserDomain, email email.SMTPService, env env.MapperData, v validator.Validator) domain.AuthService {
	return &svc{
		auth:  auth,
		user:  user,
		email: email,
		env:   env,
		v:     v,
	}
}
