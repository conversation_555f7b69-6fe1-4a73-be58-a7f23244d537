ALTER TABLE auth ADD COLUMN account_id VARCHAR(255);
ALTER TABLE auth ADD CONSTRAINT unique_account_id UNIQUE (account_id);


CREATE TABLE IF NOT EXISTS paypal_users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP,
    customer_id VARCHAR(255) NOT NULL,
    vault_card_id VARCHAR(255) NOT NULL,
    card_info JSONB,
    
    CONSTRAINT fk_paypal_users_auth FOREIGN KEY (customer_id) REFERENCES auth(account_id)
)
