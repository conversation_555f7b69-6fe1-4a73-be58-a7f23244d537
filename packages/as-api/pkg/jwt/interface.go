package jwt

import "github.com/golang-jwt/jwt"

// JWT represents JSON web token.
type JWT struct {
	Claims map[string]interface{} // Claims contains user defined claims.
	JTI    string                 // JTI field corresponds jti field. See the RFC 7519 section 4.7.1.
	String string                 // String field corresponds raw JWT string. (i.e. "xxxx.xxxx.xxxx")
}

type TokenType string

type DefaultClaims struct {
	jwt.StandardClaims
}

type CustomClaims struct {
	AID     string    `json:"aid"`
	UID     string    `json:"uid"`
	SID     *string   `json:"sid"`
	CID     string    `json:"cid"` // Customer ID (or Account ID)
	Name    *string   `json:"name"`
	Email   *string   `json:"email"`
	IsAdmin bool      `json:"iad"`
	Type    TokenType `json:"type"`

	DefaultClaims
}
