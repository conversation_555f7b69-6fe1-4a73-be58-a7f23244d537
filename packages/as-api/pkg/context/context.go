package context

import (
	"context"

	"as-api/as/pkg/jwt"

	"github.com/go-chi/chi/v5/middleware"
	"github.com/pkg/errors"
)

type Context = context.Context

func GetClaims[T any](ctx context.Context) (T, error) {
	// get claims in context
	claims, ok := ctx.Value(ClaimsCtx).(T)
	if !ok {
		return *new(T), errors.New("GetClaims: failed get claims in context")
	}

	return claims, nil
}

func WithValue(parent Context, key, val any) Context {
	return context.WithValue(parent, key, val)
}

func GetReqID(ctx context.Context) string {
	return middleware.GetReqID(ctx)
}

func Background() Context {
	return context.Background()
}

func GetUserID(ctx context.Context) (string, error) {
	claims, err := GetClaims[jwt.CustomClaims](ctx)
	if err != nil {
		return "", errors.Wrap(err, "get user ID")
	}

	return claims.UID, nil
}

func GetSellerID(ctx context.Context) (*string, error) {
	claims, err := GetClaims[jwt.CustomClaims](ctx)
	if err != nil {
		return nil, errors.Wrap(err, "get seller id")
	}

	return claims.SID, nil
}

func GetAuthID(ctx context.Context) (string, error) {
	claims, err := GetClaims[jwt.CustomClaims](ctx)
	if err != nil {
		return "", errors.Wrap(err, "get auth id")
	}

	return claims.AID, nil
}

func GetCustomerID(ctx context.Context) (string, error) {
	claims, err := GetClaims[jwt.CustomClaims](ctx)
	if err != nil {
		return "", errors.Wrap(err, "get customer id")
	}

	return claims.CID, nil
}
