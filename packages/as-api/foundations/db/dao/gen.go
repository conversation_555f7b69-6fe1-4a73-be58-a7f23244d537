// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"
	"database/sql"

	"gorm.io/gorm"

	"gorm.io/gen"

	"gorm.io/plugin/dbresolver"
)

var (
	Q                       = new(Query)
	Address                 *address
	Admin                   *admin
	AppCach                 *appCach
	Auth                    *auth
	Banner                  *banner
	Brand                   *brand
	BrandKeyword            *brandKeyword
	CartItem                *cartItem
	CommissionSale          *commissionSale
	Content                 *content
	ContentCategory         *contentCategory
	ContentType             *contentType
	Country                 *country
	CustomNgWord            *customNgWord
	DistrictTax             *districtTax
	FollowerStatistic       *followerStatistic
	Inquiry                 *inquiry
	InquiryLocationStat     *inquiryLocationStat
	Language                *language
	MaintenanceSchedule     *maintenanceSchedule
	MasterBranch            *masterBranch
	Message                 *message
	Notification            *notification
	Order                   *order
	OrderDetail             *orderDetail
	PaymentTransaction      *paymentTransaction
	PaymentTransactionLog   *paymentTransactionLog
	PaypalUser              *paypalUser
	Product                 *product
	ProductCategory         *productCategory
	ProductCategoryAncestor *productCategoryAncestor
	ProductCategoryKeyword  *productCategoryKeyword
	ProductKeyword          *productKeyword
	ProductReview           *productReview
	ProductView             *productView
	PurchaseFee             *purchaseFee
	RefreshToken            *refreshToken
	Region                  *region
	RegionAncestor          *regionAncestor
	Report                  *report
	SalesSummaryDaily       *salesSummaryDaily
	SalesSummaryMonthly     *salesSummaryMonthly
	SalesSummaryYearly      *salesSummaryYearly
	SalesTaxRate            *salesTaxRate
	SchemaMigration         *schemaMigration
	Seller                  *seller
	SellerBlockUser         *sellerBlockUser
	Subscription            *subscription
	SubscriptionFee         *subscriptionFee
	Thread                  *thread
	TransferFee             *transferFee
	UIWord                  *uIWord
	UnloggedCheck           *unloggedCheck
	User                    *user
	UserFavoriteProduct     *userFavoriteProduct
	UserFavoriteSeller      *userFavoriteSeller
	UserFcmToken            *userFcmToken
	UserNotification        *userNotification
	UserThread              *userThread
)

func SetDefault(db *gorm.DB, opts ...gen.DOOption) {
	*Q = *Use(db, opts...)
	Address = &Q.Address
	Admin = &Q.Admin
	AppCach = &Q.AppCach
	Auth = &Q.Auth
	Banner = &Q.Banner
	Brand = &Q.Brand
	BrandKeyword = &Q.BrandKeyword
	CartItem = &Q.CartItem
	CommissionSale = &Q.CommissionSale
	Content = &Q.Content
	ContentCategory = &Q.ContentCategory
	ContentType = &Q.ContentType
	Country = &Q.Country
	CustomNgWord = &Q.CustomNgWord
	DistrictTax = &Q.DistrictTax
	FollowerStatistic = &Q.FollowerStatistic
	Inquiry = &Q.Inquiry
	InquiryLocationStat = &Q.InquiryLocationStat
	Language = &Q.Language
	MaintenanceSchedule = &Q.MaintenanceSchedule
	MasterBranch = &Q.MasterBranch
	Message = &Q.Message
	Notification = &Q.Notification
	Order = &Q.Order
	OrderDetail = &Q.OrderDetail
	PaymentTransaction = &Q.PaymentTransaction
	PaymentTransactionLog = &Q.PaymentTransactionLog
	PaypalUser = &Q.PaypalUser
	Product = &Q.Product
	ProductCategory = &Q.ProductCategory
	ProductCategoryAncestor = &Q.ProductCategoryAncestor
	ProductCategoryKeyword = &Q.ProductCategoryKeyword
	ProductKeyword = &Q.ProductKeyword
	ProductReview = &Q.ProductReview
	ProductView = &Q.ProductView
	PurchaseFee = &Q.PurchaseFee
	RefreshToken = &Q.RefreshToken
	Region = &Q.Region
	RegionAncestor = &Q.RegionAncestor
	Report = &Q.Report
	SalesSummaryDaily = &Q.SalesSummaryDaily
	SalesSummaryMonthly = &Q.SalesSummaryMonthly
	SalesSummaryYearly = &Q.SalesSummaryYearly
	SalesTaxRate = &Q.SalesTaxRate
	SchemaMigration = &Q.SchemaMigration
	Seller = &Q.Seller
	SellerBlockUser = &Q.SellerBlockUser
	Subscription = &Q.Subscription
	SubscriptionFee = &Q.SubscriptionFee
	Thread = &Q.Thread
	TransferFee = &Q.TransferFee
	UIWord = &Q.UIWord
	UnloggedCheck = &Q.UnloggedCheck
	User = &Q.User
	UserFavoriteProduct = &Q.UserFavoriteProduct
	UserFavoriteSeller = &Q.UserFavoriteSeller
	UserFcmToken = &Q.UserFcmToken
	UserNotification = &Q.UserNotification
	UserThread = &Q.UserThread
}

func Use(db *gorm.DB, opts ...gen.DOOption) *Query {
	return &Query{
		db:                      db,
		Address:                 newAddress(db, opts...),
		Admin:                   newAdmin(db, opts...),
		AppCach:                 newAppCach(db, opts...),
		Auth:                    newAuth(db, opts...),
		Banner:                  newBanner(db, opts...),
		Brand:                   newBrand(db, opts...),
		BrandKeyword:            newBrandKeyword(db, opts...),
		CartItem:                newCartItem(db, opts...),
		CommissionSale:          newCommissionSale(db, opts...),
		Content:                 newContent(db, opts...),
		ContentCategory:         newContentCategory(db, opts...),
		ContentType:             newContentType(db, opts...),
		Country:                 newCountry(db, opts...),
		CustomNgWord:            newCustomNgWord(db, opts...),
		DistrictTax:             newDistrictTax(db, opts...),
		FollowerStatistic:       newFollowerStatistic(db, opts...),
		Inquiry:                 newInquiry(db, opts...),
		InquiryLocationStat:     newInquiryLocationStat(db, opts...),
		Language:                newLanguage(db, opts...),
		MaintenanceSchedule:     newMaintenanceSchedule(db, opts...),
		MasterBranch:            newMasterBranch(db, opts...),
		Message:                 newMessage(db, opts...),
		Notification:            newNotification(db, opts...),
		Order:                   newOrder(db, opts...),
		OrderDetail:             newOrderDetail(db, opts...),
		PaymentTransaction:      newPaymentTransaction(db, opts...),
		PaymentTransactionLog:   newPaymentTransactionLog(db, opts...),
		PaypalUser:              newPaypalUser(db, opts...),
		Product:                 newProduct(db, opts...),
		ProductCategory:         newProductCategory(db, opts...),
		ProductCategoryAncestor: newProductCategoryAncestor(db, opts...),
		ProductCategoryKeyword:  newProductCategoryKeyword(db, opts...),
		ProductKeyword:          newProductKeyword(db, opts...),
		ProductReview:           newProductReview(db, opts...),
		ProductView:             newProductView(db, opts...),
		PurchaseFee:             newPurchaseFee(db, opts...),
		RefreshToken:            newRefreshToken(db, opts...),
		Region:                  newRegion(db, opts...),
		RegionAncestor:          newRegionAncestor(db, opts...),
		Report:                  newReport(db, opts...),
		SalesSummaryDaily:       newSalesSummaryDaily(db, opts...),
		SalesSummaryMonthly:     newSalesSummaryMonthly(db, opts...),
		SalesSummaryYearly:      newSalesSummaryYearly(db, opts...),
		SalesTaxRate:            newSalesTaxRate(db, opts...),
		SchemaMigration:         newSchemaMigration(db, opts...),
		Seller:                  newSeller(db, opts...),
		SellerBlockUser:         newSellerBlockUser(db, opts...),
		Subscription:            newSubscription(db, opts...),
		SubscriptionFee:         newSubscriptionFee(db, opts...),
		Thread:                  newThread(db, opts...),
		TransferFee:             newTransferFee(db, opts...),
		UIWord:                  newUIWord(db, opts...),
		UnloggedCheck:           newUnloggedCheck(db, opts...),
		User:                    newUser(db, opts...),
		UserFavoriteProduct:     newUserFavoriteProduct(db, opts...),
		UserFavoriteSeller:      newUserFavoriteSeller(db, opts...),
		UserFcmToken:            newUserFcmToken(db, opts...),
		UserNotification:        newUserNotification(db, opts...),
		UserThread:              newUserThread(db, opts...),
	}
}

type Query struct {
	db *gorm.DB

	Address                 address
	Admin                   admin
	AppCach                 appCach
	Auth                    auth
	Banner                  banner
	Brand                   brand
	BrandKeyword            brandKeyword
	CartItem                cartItem
	CommissionSale          commissionSale
	Content                 content
	ContentCategory         contentCategory
	ContentType             contentType
	Country                 country
	CustomNgWord            customNgWord
	DistrictTax             districtTax
	FollowerStatistic       followerStatistic
	Inquiry                 inquiry
	InquiryLocationStat     inquiryLocationStat
	Language                language
	MaintenanceSchedule     maintenanceSchedule
	MasterBranch            masterBranch
	Message                 message
	Notification            notification
	Order                   order
	OrderDetail             orderDetail
	PaymentTransaction      paymentTransaction
	PaymentTransactionLog   paymentTransactionLog
	PaypalUser              paypalUser
	Product                 product
	ProductCategory         productCategory
	ProductCategoryAncestor productCategoryAncestor
	ProductCategoryKeyword  productCategoryKeyword
	ProductKeyword          productKeyword
	ProductReview           productReview
	ProductView             productView
	PurchaseFee             purchaseFee
	RefreshToken            refreshToken
	Region                  region
	RegionAncestor          regionAncestor
	Report                  report
	SalesSummaryDaily       salesSummaryDaily
	SalesSummaryMonthly     salesSummaryMonthly
	SalesSummaryYearly      salesSummaryYearly
	SalesTaxRate            salesTaxRate
	SchemaMigration         schemaMigration
	Seller                  seller
	SellerBlockUser         sellerBlockUser
	Subscription            subscription
	SubscriptionFee         subscriptionFee
	Thread                  thread
	TransferFee             transferFee
	UIWord                  uIWord
	UnloggedCheck           unloggedCheck
	User                    user
	UserFavoriteProduct     userFavoriteProduct
	UserFavoriteSeller      userFavoriteSeller
	UserFcmToken            userFcmToken
	UserNotification        userNotification
	UserThread              userThread
}

func (q *Query) Available() bool { return q.db != nil }

func (q *Query) clone(db *gorm.DB) *Query {
	return &Query{
		db:                      db,
		Address:                 q.Address.clone(db),
		Admin:                   q.Admin.clone(db),
		AppCach:                 q.AppCach.clone(db),
		Auth:                    q.Auth.clone(db),
		Banner:                  q.Banner.clone(db),
		Brand:                   q.Brand.clone(db),
		BrandKeyword:            q.BrandKeyword.clone(db),
		CartItem:                q.CartItem.clone(db),
		CommissionSale:          q.CommissionSale.clone(db),
		Content:                 q.Content.clone(db),
		ContentCategory:         q.ContentCategory.clone(db),
		ContentType:             q.ContentType.clone(db),
		Country:                 q.Country.clone(db),
		CustomNgWord:            q.CustomNgWord.clone(db),
		DistrictTax:             q.DistrictTax.clone(db),
		FollowerStatistic:       q.FollowerStatistic.clone(db),
		Inquiry:                 q.Inquiry.clone(db),
		InquiryLocationStat:     q.InquiryLocationStat.clone(db),
		Language:                q.Language.clone(db),
		MaintenanceSchedule:     q.MaintenanceSchedule.clone(db),
		MasterBranch:            q.MasterBranch.clone(db),
		Message:                 q.Message.clone(db),
		Notification:            q.Notification.clone(db),
		Order:                   q.Order.clone(db),
		OrderDetail:             q.OrderDetail.clone(db),
		PaymentTransaction:      q.PaymentTransaction.clone(db),
		PaymentTransactionLog:   q.PaymentTransactionLog.clone(db),
		PaypalUser:              q.PaypalUser.clone(db),
		Product:                 q.Product.clone(db),
		ProductCategory:         q.ProductCategory.clone(db),
		ProductCategoryAncestor: q.ProductCategoryAncestor.clone(db),
		ProductCategoryKeyword:  q.ProductCategoryKeyword.clone(db),
		ProductKeyword:          q.ProductKeyword.clone(db),
		ProductReview:           q.ProductReview.clone(db),
		ProductView:             q.ProductView.clone(db),
		PurchaseFee:             q.PurchaseFee.clone(db),
		RefreshToken:            q.RefreshToken.clone(db),
		Region:                  q.Region.clone(db),
		RegionAncestor:          q.RegionAncestor.clone(db),
		Report:                  q.Report.clone(db),
		SalesSummaryDaily:       q.SalesSummaryDaily.clone(db),
		SalesSummaryMonthly:     q.SalesSummaryMonthly.clone(db),
		SalesSummaryYearly:      q.SalesSummaryYearly.clone(db),
		SalesTaxRate:            q.SalesTaxRate.clone(db),
		SchemaMigration:         q.SchemaMigration.clone(db),
		Seller:                  q.Seller.clone(db),
		SellerBlockUser:         q.SellerBlockUser.clone(db),
		Subscription:            q.Subscription.clone(db),
		SubscriptionFee:         q.SubscriptionFee.clone(db),
		Thread:                  q.Thread.clone(db),
		TransferFee:             q.TransferFee.clone(db),
		UIWord:                  q.UIWord.clone(db),
		UnloggedCheck:           q.UnloggedCheck.clone(db),
		User:                    q.User.clone(db),
		UserFavoriteProduct:     q.UserFavoriteProduct.clone(db),
		UserFavoriteSeller:      q.UserFavoriteSeller.clone(db),
		UserFcmToken:            q.UserFcmToken.clone(db),
		UserNotification:        q.UserNotification.clone(db),
		UserThread:              q.UserThread.clone(db),
	}
}

func (q *Query) ReadDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Read))
}

func (q *Query) WriteDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Write))
}

func (q *Query) ReplaceDB(db *gorm.DB) *Query {
	return &Query{
		db:                      db,
		Address:                 q.Address.replaceDB(db),
		Admin:                   q.Admin.replaceDB(db),
		AppCach:                 q.AppCach.replaceDB(db),
		Auth:                    q.Auth.replaceDB(db),
		Banner:                  q.Banner.replaceDB(db),
		Brand:                   q.Brand.replaceDB(db),
		BrandKeyword:            q.BrandKeyword.replaceDB(db),
		CartItem:                q.CartItem.replaceDB(db),
		CommissionSale:          q.CommissionSale.replaceDB(db),
		Content:                 q.Content.replaceDB(db),
		ContentCategory:         q.ContentCategory.replaceDB(db),
		ContentType:             q.ContentType.replaceDB(db),
		Country:                 q.Country.replaceDB(db),
		CustomNgWord:            q.CustomNgWord.replaceDB(db),
		DistrictTax:             q.DistrictTax.replaceDB(db),
		FollowerStatistic:       q.FollowerStatistic.replaceDB(db),
		Inquiry:                 q.Inquiry.replaceDB(db),
		InquiryLocationStat:     q.InquiryLocationStat.replaceDB(db),
		Language:                q.Language.replaceDB(db),
		MaintenanceSchedule:     q.MaintenanceSchedule.replaceDB(db),
		MasterBranch:            q.MasterBranch.replaceDB(db),
		Message:                 q.Message.replaceDB(db),
		Notification:            q.Notification.replaceDB(db),
		Order:                   q.Order.replaceDB(db),
		OrderDetail:             q.OrderDetail.replaceDB(db),
		PaymentTransaction:      q.PaymentTransaction.replaceDB(db),
		PaymentTransactionLog:   q.PaymentTransactionLog.replaceDB(db),
		PaypalUser:              q.PaypalUser.replaceDB(db),
		Product:                 q.Product.replaceDB(db),
		ProductCategory:         q.ProductCategory.replaceDB(db),
		ProductCategoryAncestor: q.ProductCategoryAncestor.replaceDB(db),
		ProductCategoryKeyword:  q.ProductCategoryKeyword.replaceDB(db),
		ProductKeyword:          q.ProductKeyword.replaceDB(db),
		ProductReview:           q.ProductReview.replaceDB(db),
		ProductView:             q.ProductView.replaceDB(db),
		PurchaseFee:             q.PurchaseFee.replaceDB(db),
		RefreshToken:            q.RefreshToken.replaceDB(db),
		Region:                  q.Region.replaceDB(db),
		RegionAncestor:          q.RegionAncestor.replaceDB(db),
		Report:                  q.Report.replaceDB(db),
		SalesSummaryDaily:       q.SalesSummaryDaily.replaceDB(db),
		SalesSummaryMonthly:     q.SalesSummaryMonthly.replaceDB(db),
		SalesSummaryYearly:      q.SalesSummaryYearly.replaceDB(db),
		SalesTaxRate:            q.SalesTaxRate.replaceDB(db),
		SchemaMigration:         q.SchemaMigration.replaceDB(db),
		Seller:                  q.Seller.replaceDB(db),
		SellerBlockUser:         q.SellerBlockUser.replaceDB(db),
		Subscription:            q.Subscription.replaceDB(db),
		SubscriptionFee:         q.SubscriptionFee.replaceDB(db),
		Thread:                  q.Thread.replaceDB(db),
		TransferFee:             q.TransferFee.replaceDB(db),
		UIWord:                  q.UIWord.replaceDB(db),
		UnloggedCheck:           q.UnloggedCheck.replaceDB(db),
		User:                    q.User.replaceDB(db),
		UserFavoriteProduct:     q.UserFavoriteProduct.replaceDB(db),
		UserFavoriteSeller:      q.UserFavoriteSeller.replaceDB(db),
		UserFcmToken:            q.UserFcmToken.replaceDB(db),
		UserNotification:        q.UserNotification.replaceDB(db),
		UserThread:              q.UserThread.replaceDB(db),
	}
}

type queryCtx struct {
	Address                 IAddressDo
	Admin                   IAdminDo
	AppCach                 IAppCachDo
	Auth                    IAuthDo
	Banner                  IBannerDo
	Brand                   IBrandDo
	BrandKeyword            IBrandKeywordDo
	CartItem                ICartItemDo
	CommissionSale          ICommissionSaleDo
	Content                 IContentDo
	ContentCategory         IContentCategoryDo
	ContentType             IContentTypeDo
	Country                 ICountryDo
	CustomNgWord            ICustomNgWordDo
	DistrictTax             IDistrictTaxDo
	FollowerStatistic       IFollowerStatisticDo
	Inquiry                 IInquiryDo
	InquiryLocationStat     IInquiryLocationStatDo
	Language                ILanguageDo
	MaintenanceSchedule     IMaintenanceScheduleDo
	MasterBranch            IMasterBranchDo
	Message                 IMessageDo
	Notification            INotificationDo
	Order                   IOrderDo
	OrderDetail             IOrderDetailDo
	PaymentTransaction      IPaymentTransactionDo
	PaymentTransactionLog   IPaymentTransactionLogDo
	PaypalUser              IPaypalUserDo
	Product                 IProductDo
	ProductCategory         IProductCategoryDo
	ProductCategoryAncestor IProductCategoryAncestorDo
	ProductCategoryKeyword  IProductCategoryKeywordDo
	ProductKeyword          IProductKeywordDo
	ProductReview           IProductReviewDo
	ProductView             IProductViewDo
	PurchaseFee             IPurchaseFeeDo
	RefreshToken            IRefreshTokenDo
	Region                  IRegionDo
	RegionAncestor          IRegionAncestorDo
	Report                  IReportDo
	SalesSummaryDaily       ISalesSummaryDailyDo
	SalesSummaryMonthly     ISalesSummaryMonthlyDo
	SalesSummaryYearly      ISalesSummaryYearlyDo
	SalesTaxRate            ISalesTaxRateDo
	SchemaMigration         ISchemaMigrationDo
	Seller                  ISellerDo
	SellerBlockUser         ISellerBlockUserDo
	Subscription            ISubscriptionDo
	SubscriptionFee         ISubscriptionFeeDo
	Thread                  IThreadDo
	TransferFee             ITransferFeeDo
	UIWord                  IUIWordDo
	UnloggedCheck           IUnloggedCheckDo
	User                    IUserDo
	UserFavoriteProduct     IUserFavoriteProductDo
	UserFavoriteSeller      IUserFavoriteSellerDo
	UserFcmToken            IUserFcmTokenDo
	UserNotification        IUserNotificationDo
	UserThread              IUserThreadDo
}

func (q *Query) WithContext(ctx context.Context) *queryCtx {
	return &queryCtx{
		Address:                 q.Address.WithContext(ctx),
		Admin:                   q.Admin.WithContext(ctx),
		AppCach:                 q.AppCach.WithContext(ctx),
		Auth:                    q.Auth.WithContext(ctx),
		Banner:                  q.Banner.WithContext(ctx),
		Brand:                   q.Brand.WithContext(ctx),
		BrandKeyword:            q.BrandKeyword.WithContext(ctx),
		CartItem:                q.CartItem.WithContext(ctx),
		CommissionSale:          q.CommissionSale.WithContext(ctx),
		Content:                 q.Content.WithContext(ctx),
		ContentCategory:         q.ContentCategory.WithContext(ctx),
		ContentType:             q.ContentType.WithContext(ctx),
		Country:                 q.Country.WithContext(ctx),
		CustomNgWord:            q.CustomNgWord.WithContext(ctx),
		DistrictTax:             q.DistrictTax.WithContext(ctx),
		FollowerStatistic:       q.FollowerStatistic.WithContext(ctx),
		Inquiry:                 q.Inquiry.WithContext(ctx),
		InquiryLocationStat:     q.InquiryLocationStat.WithContext(ctx),
		Language:                q.Language.WithContext(ctx),
		MaintenanceSchedule:     q.MaintenanceSchedule.WithContext(ctx),
		MasterBranch:            q.MasterBranch.WithContext(ctx),
		Message:                 q.Message.WithContext(ctx),
		Notification:            q.Notification.WithContext(ctx),
		Order:                   q.Order.WithContext(ctx),
		OrderDetail:             q.OrderDetail.WithContext(ctx),
		PaymentTransaction:      q.PaymentTransaction.WithContext(ctx),
		PaymentTransactionLog:   q.PaymentTransactionLog.WithContext(ctx),
		PaypalUser:              q.PaypalUser.WithContext(ctx),
		Product:                 q.Product.WithContext(ctx),
		ProductCategory:         q.ProductCategory.WithContext(ctx),
		ProductCategoryAncestor: q.ProductCategoryAncestor.WithContext(ctx),
		ProductCategoryKeyword:  q.ProductCategoryKeyword.WithContext(ctx),
		ProductKeyword:          q.ProductKeyword.WithContext(ctx),
		ProductReview:           q.ProductReview.WithContext(ctx),
		ProductView:             q.ProductView.WithContext(ctx),
		PurchaseFee:             q.PurchaseFee.WithContext(ctx),
		RefreshToken:            q.RefreshToken.WithContext(ctx),
		Region:                  q.Region.WithContext(ctx),
		RegionAncestor:          q.RegionAncestor.WithContext(ctx),
		Report:                  q.Report.WithContext(ctx),
		SalesSummaryDaily:       q.SalesSummaryDaily.WithContext(ctx),
		SalesSummaryMonthly:     q.SalesSummaryMonthly.WithContext(ctx),
		SalesSummaryYearly:      q.SalesSummaryYearly.WithContext(ctx),
		SalesTaxRate:            q.SalesTaxRate.WithContext(ctx),
		SchemaMigration:         q.SchemaMigration.WithContext(ctx),
		Seller:                  q.Seller.WithContext(ctx),
		SellerBlockUser:         q.SellerBlockUser.WithContext(ctx),
		Subscription:            q.Subscription.WithContext(ctx),
		SubscriptionFee:         q.SubscriptionFee.WithContext(ctx),
		Thread:                  q.Thread.WithContext(ctx),
		TransferFee:             q.TransferFee.WithContext(ctx),
		UIWord:                  q.UIWord.WithContext(ctx),
		UnloggedCheck:           q.UnloggedCheck.WithContext(ctx),
		User:                    q.User.WithContext(ctx),
		UserFavoriteProduct:     q.UserFavoriteProduct.WithContext(ctx),
		UserFavoriteSeller:      q.UserFavoriteSeller.WithContext(ctx),
		UserFcmToken:            q.UserFcmToken.WithContext(ctx),
		UserNotification:        q.UserNotification.WithContext(ctx),
		UserThread:              q.UserThread.WithContext(ctx),
	}
}

func (q *Query) Transaction(fc func(tx *Query) error, opts ...*sql.TxOptions) error {
	return q.db.Transaction(func(tx *gorm.DB) error { return fc(q.clone(tx)) }, opts...)
}

func (q *Query) Begin(opts ...*sql.TxOptions) *QueryTx {
	tx := q.db.Begin(opts...)
	return &QueryTx{Query: q.clone(tx), Error: tx.Error}
}

type QueryTx struct {
	*Query
	Error error
}

func (q *QueryTx) Commit() error {
	return q.db.Commit().Error
}

func (q *QueryTx) Rollback() error {
	return q.db.Rollback().Error
}

func (q *QueryTx) SavePoint(name string) error {
	return q.db.SavePoint(name).Error
}

func (q *QueryTx) RollbackTo(name string) error {
	return q.db.RollbackTo(name).Error
}
