// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"as-api/as/foundations/db/entities"
)

func newFollowerStatistic(db *gorm.DB, opts ...gen.DOOption) followerStatistic {
	_followerStatistic := followerStatistic{}

	_followerStatistic.followerStatisticDo.UseDB(db, opts...)
	_followerStatistic.followerStatisticDo.UseModel(&entities.FollowerStatistic{})

	tableName := _followerStatistic.followerStatisticDo.TableName()
	_followerStatistic.ALL = field.NewAsterisk(tableName)
	_followerStatistic.ID = field.NewString(tableName, "id")
	_followerStatistic.CreatedAt = field.NewTime(tableName, "created_at")
	_followerStatistic.UpdatedAt = field.NewTime(tableName, "updated_at")
	_followerStatistic.SellerID = field.NewString(tableName, "seller_id")
	_followerStatistic.CountryCode = field.NewString(tableName, "country_code")
	_followerStatistic.Gender = field.NewString(tableName, "gender")
	_followerStatistic.AgeGroup = field.NewString(tableName, "age_group")
	_followerStatistic.FollowerCount = field.NewInt32(tableName, "follower_count")
	_followerStatistic.CalculatedAt = field.NewTime(tableName, "calculated_at")

	_followerStatistic.fillFieldMap()

	return _followerStatistic
}

type followerStatistic struct {
	followerStatisticDo

	ALL           field.Asterisk
	ID            field.String
	CreatedAt     field.Time
	UpdatedAt     field.Time
	SellerID      field.String
	CountryCode   field.String // ISO 3166-1 alpha-3 country code (e.g., USA, JPN, GBR) - primary country identifier
	Gender        field.String // Follower gender: male, female, or other
	AgeGroup      field.String // Age group ranges: under-20, 20-29, 30-39, 40-49, 50-59, 60-and-over
	FollowerCount field.Int32  // Number of followers in this demographic segment
	CalculatedAt  field.Time   // Timestamp when this statistic was last calculated

	fieldMap map[string]field.Expr
}

func (f followerStatistic) Table(newTableName string) *followerStatistic {
	f.followerStatisticDo.UseTable(newTableName)
	return f.updateTableName(newTableName)
}

func (f followerStatistic) As(alias string) *followerStatistic {
	f.followerStatisticDo.DO = *(f.followerStatisticDo.As(alias).(*gen.DO))
	return f.updateTableName(alias)
}

func (f *followerStatistic) updateTableName(table string) *followerStatistic {
	f.ALL = field.NewAsterisk(table)
	f.ID = field.NewString(table, "id")
	f.CreatedAt = field.NewTime(table, "created_at")
	f.UpdatedAt = field.NewTime(table, "updated_at")
	f.SellerID = field.NewString(table, "seller_id")
	f.CountryCode = field.NewString(table, "country_code")
	f.Gender = field.NewString(table, "gender")
	f.AgeGroup = field.NewString(table, "age_group")
	f.FollowerCount = field.NewInt32(table, "follower_count")
	f.CalculatedAt = field.NewTime(table, "calculated_at")

	f.fillFieldMap()

	return f
}

func (f *followerStatistic) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := f.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (f *followerStatistic) fillFieldMap() {
	f.fieldMap = make(map[string]field.Expr, 9)
	f.fieldMap["id"] = f.ID
	f.fieldMap["created_at"] = f.CreatedAt
	f.fieldMap["updated_at"] = f.UpdatedAt
	f.fieldMap["seller_id"] = f.SellerID
	f.fieldMap["country_code"] = f.CountryCode
	f.fieldMap["gender"] = f.Gender
	f.fieldMap["age_group"] = f.AgeGroup
	f.fieldMap["follower_count"] = f.FollowerCount
	f.fieldMap["calculated_at"] = f.CalculatedAt
}

func (f followerStatistic) clone(db *gorm.DB) followerStatistic {
	f.followerStatisticDo.ReplaceConnPool(db.Statement.ConnPool)
	return f
}

func (f followerStatistic) replaceDB(db *gorm.DB) followerStatistic {
	f.followerStatisticDo.ReplaceDB(db)
	return f
}

type followerStatisticDo struct{ gen.DO }

type IFollowerStatisticDo interface {
	gen.SubQuery
	Debug() IFollowerStatisticDo
	WithContext(ctx context.Context) IFollowerStatisticDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IFollowerStatisticDo
	WriteDB() IFollowerStatisticDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IFollowerStatisticDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IFollowerStatisticDo
	Not(conds ...gen.Condition) IFollowerStatisticDo
	Or(conds ...gen.Condition) IFollowerStatisticDo
	Select(conds ...field.Expr) IFollowerStatisticDo
	Where(conds ...gen.Condition) IFollowerStatisticDo
	Order(conds ...field.Expr) IFollowerStatisticDo
	Distinct(cols ...field.Expr) IFollowerStatisticDo
	Omit(cols ...field.Expr) IFollowerStatisticDo
	Join(table schema.Tabler, on ...field.Expr) IFollowerStatisticDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IFollowerStatisticDo
	RightJoin(table schema.Tabler, on ...field.Expr) IFollowerStatisticDo
	Group(cols ...field.Expr) IFollowerStatisticDo
	Having(conds ...gen.Condition) IFollowerStatisticDo
	Limit(limit int) IFollowerStatisticDo
	Offset(offset int) IFollowerStatisticDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IFollowerStatisticDo
	Unscoped() IFollowerStatisticDo
	Create(values ...*entities.FollowerStatistic) error
	CreateInBatches(values []*entities.FollowerStatistic, batchSize int) error
	Save(values ...*entities.FollowerStatistic) error
	First() (*entities.FollowerStatistic, error)
	Take() (*entities.FollowerStatistic, error)
	Last() (*entities.FollowerStatistic, error)
	Find() ([]*entities.FollowerStatistic, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entities.FollowerStatistic, err error)
	FindInBatches(result *[]*entities.FollowerStatistic, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*entities.FollowerStatistic) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IFollowerStatisticDo
	Assign(attrs ...field.AssignExpr) IFollowerStatisticDo
	Joins(fields ...field.RelationField) IFollowerStatisticDo
	Preload(fields ...field.RelationField) IFollowerStatisticDo
	FirstOrInit() (*entities.FollowerStatistic, error)
	FirstOrCreate() (*entities.FollowerStatistic, error)
	FindByPage(offset int, limit int) (result []*entities.FollowerStatistic, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IFollowerStatisticDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (f followerStatisticDo) Debug() IFollowerStatisticDo {
	return f.withDO(f.DO.Debug())
}

func (f followerStatisticDo) WithContext(ctx context.Context) IFollowerStatisticDo {
	return f.withDO(f.DO.WithContext(ctx))
}

func (f followerStatisticDo) ReadDB() IFollowerStatisticDo {
	return f.Clauses(dbresolver.Read)
}

func (f followerStatisticDo) WriteDB() IFollowerStatisticDo {
	return f.Clauses(dbresolver.Write)
}

func (f followerStatisticDo) Session(config *gorm.Session) IFollowerStatisticDo {
	return f.withDO(f.DO.Session(config))
}

func (f followerStatisticDo) Clauses(conds ...clause.Expression) IFollowerStatisticDo {
	return f.withDO(f.DO.Clauses(conds...))
}

func (f followerStatisticDo) Returning(value interface{}, columns ...string) IFollowerStatisticDo {
	return f.withDO(f.DO.Returning(value, columns...))
}

func (f followerStatisticDo) Not(conds ...gen.Condition) IFollowerStatisticDo {
	return f.withDO(f.DO.Not(conds...))
}

func (f followerStatisticDo) Or(conds ...gen.Condition) IFollowerStatisticDo {
	return f.withDO(f.DO.Or(conds...))
}

func (f followerStatisticDo) Select(conds ...field.Expr) IFollowerStatisticDo {
	return f.withDO(f.DO.Select(conds...))
}

func (f followerStatisticDo) Where(conds ...gen.Condition) IFollowerStatisticDo {
	return f.withDO(f.DO.Where(conds...))
}

func (f followerStatisticDo) Order(conds ...field.Expr) IFollowerStatisticDo {
	return f.withDO(f.DO.Order(conds...))
}

func (f followerStatisticDo) Distinct(cols ...field.Expr) IFollowerStatisticDo {
	return f.withDO(f.DO.Distinct(cols...))
}

func (f followerStatisticDo) Omit(cols ...field.Expr) IFollowerStatisticDo {
	return f.withDO(f.DO.Omit(cols...))
}

func (f followerStatisticDo) Join(table schema.Tabler, on ...field.Expr) IFollowerStatisticDo {
	return f.withDO(f.DO.Join(table, on...))
}

func (f followerStatisticDo) LeftJoin(table schema.Tabler, on ...field.Expr) IFollowerStatisticDo {
	return f.withDO(f.DO.LeftJoin(table, on...))
}

func (f followerStatisticDo) RightJoin(table schema.Tabler, on ...field.Expr) IFollowerStatisticDo {
	return f.withDO(f.DO.RightJoin(table, on...))
}

func (f followerStatisticDo) Group(cols ...field.Expr) IFollowerStatisticDo {
	return f.withDO(f.DO.Group(cols...))
}

func (f followerStatisticDo) Having(conds ...gen.Condition) IFollowerStatisticDo {
	return f.withDO(f.DO.Having(conds...))
}

func (f followerStatisticDo) Limit(limit int) IFollowerStatisticDo {
	return f.withDO(f.DO.Limit(limit))
}

func (f followerStatisticDo) Offset(offset int) IFollowerStatisticDo {
	return f.withDO(f.DO.Offset(offset))
}

func (f followerStatisticDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IFollowerStatisticDo {
	return f.withDO(f.DO.Scopes(funcs...))
}

func (f followerStatisticDo) Unscoped() IFollowerStatisticDo {
	return f.withDO(f.DO.Unscoped())
}

func (f followerStatisticDo) Create(values ...*entities.FollowerStatistic) error {
	if len(values) == 0 {
		return nil
	}
	return f.DO.Create(values)
}

func (f followerStatisticDo) CreateInBatches(values []*entities.FollowerStatistic, batchSize int) error {
	return f.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (f followerStatisticDo) Save(values ...*entities.FollowerStatistic) error {
	if len(values) == 0 {
		return nil
	}
	return f.DO.Save(values)
}

func (f followerStatisticDo) First() (*entities.FollowerStatistic, error) {
	if result, err := f.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entities.FollowerStatistic), nil
	}
}

func (f followerStatisticDo) Take() (*entities.FollowerStatistic, error) {
	if result, err := f.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entities.FollowerStatistic), nil
	}
}

func (f followerStatisticDo) Last() (*entities.FollowerStatistic, error) {
	if result, err := f.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entities.FollowerStatistic), nil
	}
}

func (f followerStatisticDo) Find() ([]*entities.FollowerStatistic, error) {
	result, err := f.DO.Find()
	return result.([]*entities.FollowerStatistic), err
}

func (f followerStatisticDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entities.FollowerStatistic, err error) {
	buf := make([]*entities.FollowerStatistic, 0, batchSize)
	err = f.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (f followerStatisticDo) FindInBatches(result *[]*entities.FollowerStatistic, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return f.DO.FindInBatches(result, batchSize, fc)
}

func (f followerStatisticDo) Attrs(attrs ...field.AssignExpr) IFollowerStatisticDo {
	return f.withDO(f.DO.Attrs(attrs...))
}

func (f followerStatisticDo) Assign(attrs ...field.AssignExpr) IFollowerStatisticDo {
	return f.withDO(f.DO.Assign(attrs...))
}

func (f followerStatisticDo) Joins(fields ...field.RelationField) IFollowerStatisticDo {
	for _, _f := range fields {
		f = *f.withDO(f.DO.Joins(_f))
	}
	return &f
}

func (f followerStatisticDo) Preload(fields ...field.RelationField) IFollowerStatisticDo {
	for _, _f := range fields {
		f = *f.withDO(f.DO.Preload(_f))
	}
	return &f
}

func (f followerStatisticDo) FirstOrInit() (*entities.FollowerStatistic, error) {
	if result, err := f.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entities.FollowerStatistic), nil
	}
}

func (f followerStatisticDo) FirstOrCreate() (*entities.FollowerStatistic, error) {
	if result, err := f.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entities.FollowerStatistic), nil
	}
}

func (f followerStatisticDo) FindByPage(offset int, limit int) (result []*entities.FollowerStatistic, count int64, err error) {
	result, err = f.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = f.Offset(-1).Limit(-1).Count()
	return
}

func (f followerStatisticDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = f.Count()
	if err != nil {
		return
	}

	err = f.Offset(offset).Limit(limit).Scan(result)
	return
}

func (f followerStatisticDo) Scan(result interface{}) (err error) {
	return f.DO.Scan(result)
}

func (f followerStatisticDo) Delete(models ...*entities.FollowerStatistic) (result gen.ResultInfo, err error) {
	return f.DO.Delete(models)
}

func (f *followerStatisticDo) withDO(do gen.Dao) *followerStatisticDo {
	f.DO = *do.(*gen.DO)
	return f
}
