// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"as-api/as/foundations/db/entities"
)

func newOrderDetail(db *gorm.DB, opts ...gen.DOOption) orderDetail {
	_orderDetail := orderDetail{}

	_orderDetail.orderDetailDo.UseDB(db, opts...)
	_orderDetail.orderDetailDo.UseModel(&entities.OrderDetail{})

	tableName := _orderDetail.orderDetailDo.TableName()
	_orderDetail.ALL = field.NewAsterisk(tableName)
	_orderDetail.ID = field.NewString(tableName, "id")
	_orderDetail.CreatedAt = field.NewTime(tableName, "created_at")
	_orderDetail.UpdatedAt = field.NewTime(tableName, "updated_at")
	_orderDetail.OrderID = field.NewString(tableName, "order_id")
	_orderDetail.ProductID = field.NewString(tableName, "product_id")
	_orderDetail.Price = field.NewFloat64(tableName, "price")
	_orderDetail.TransactionStatus = field.NewString(tableName, "transaction_status")
	_orderDetail.TaxRate = field.NewFloat64(tableName, "tax_rate")
	_orderDetail.DiscountAmount = field.NewFloat64(tableName, "discount_amount")
	_orderDetail.TotalPrice = field.NewFloat64(tableName, "total_price")
	_orderDetail.WaybillNumber = field.NewString(tableName, "waybill_number")
	_orderDetail.TaxAmount = field.NewFloat64(tableName, "tax_amount")
	_orderDetail.OrderEnd = field.NewTime(tableName, "order_end")
	_orderDetail.OrderNumber = field.NewString(tableName, "order_number")
	_orderDetail.TaxInfos = field.NewField(tableName, "tax_infos")
	_orderDetail.ShippedDate = field.NewTime(tableName, "shipped_date")
	_orderDetail.CompletedDate = field.NewTime(tableName, "completed_date")
	_orderDetail.Note = field.NewString(tableName, "note")
	_orderDetail.ReceivedDate = field.NewTime(tableName, "received_date")
	_orderDetail.SalesAmount = field.NewFloat64(tableName, "sales_amount")
	_orderDetail.SalesInfo = field.NewField(tableName, "sales_info")
	_orderDetail.IsInternational = field.NewBool(tableName, "is_international")

	_orderDetail.fillFieldMap()

	return _orderDetail
}

type orderDetail struct {
	orderDetailDo

	ALL               field.Asterisk
	ID                field.String
	CreatedAt         field.Time
	UpdatedAt         field.Time
	OrderID           field.String
	ProductID         field.String
	Price             field.Float64
	TransactionStatus field.String
	TaxRate           field.Float64
	DiscountAmount    field.Float64
	TotalPrice        field.Float64
	WaybillNumber     field.String
	TaxAmount         field.Float64
	OrderEnd          field.Time
	OrderNumber       field.String
	TaxInfos          field.Field
	ShippedDate       field.Time
	CompletedDate     field.Time
	Note              field.String
	ReceivedDate      field.Time
	SalesAmount       field.Float64
	SalesInfo         field.Field
	IsInternational   field.Bool

	fieldMap map[string]field.Expr
}

func (o orderDetail) Table(newTableName string) *orderDetail {
	o.orderDetailDo.UseTable(newTableName)
	return o.updateTableName(newTableName)
}

func (o orderDetail) As(alias string) *orderDetail {
	o.orderDetailDo.DO = *(o.orderDetailDo.As(alias).(*gen.DO))
	return o.updateTableName(alias)
}

func (o *orderDetail) updateTableName(table string) *orderDetail {
	o.ALL = field.NewAsterisk(table)
	o.ID = field.NewString(table, "id")
	o.CreatedAt = field.NewTime(table, "created_at")
	o.UpdatedAt = field.NewTime(table, "updated_at")
	o.OrderID = field.NewString(table, "order_id")
	o.ProductID = field.NewString(table, "product_id")
	o.Price = field.NewFloat64(table, "price")
	o.TransactionStatus = field.NewString(table, "transaction_status")
	o.TaxRate = field.NewFloat64(table, "tax_rate")
	o.DiscountAmount = field.NewFloat64(table, "discount_amount")
	o.TotalPrice = field.NewFloat64(table, "total_price")
	o.WaybillNumber = field.NewString(table, "waybill_number")
	o.TaxAmount = field.NewFloat64(table, "tax_amount")
	o.OrderEnd = field.NewTime(table, "order_end")
	o.OrderNumber = field.NewString(table, "order_number")
	o.TaxInfos = field.NewField(table, "tax_infos")
	o.ShippedDate = field.NewTime(table, "shipped_date")
	o.CompletedDate = field.NewTime(table, "completed_date")
	o.Note = field.NewString(table, "note")
	o.ReceivedDate = field.NewTime(table, "received_date")
	o.SalesAmount = field.NewFloat64(table, "sales_amount")
	o.SalesInfo = field.NewField(table, "sales_info")
	o.IsInternational = field.NewBool(table, "is_international")

	o.fillFieldMap()

	return o
}

func (o *orderDetail) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := o.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (o *orderDetail) fillFieldMap() {
	o.fieldMap = make(map[string]field.Expr, 22)
	o.fieldMap["id"] = o.ID
	o.fieldMap["created_at"] = o.CreatedAt
	o.fieldMap["updated_at"] = o.UpdatedAt
	o.fieldMap["order_id"] = o.OrderID
	o.fieldMap["product_id"] = o.ProductID
	o.fieldMap["price"] = o.Price
	o.fieldMap["transaction_status"] = o.TransactionStatus
	o.fieldMap["tax_rate"] = o.TaxRate
	o.fieldMap["discount_amount"] = o.DiscountAmount
	o.fieldMap["total_price"] = o.TotalPrice
	o.fieldMap["waybill_number"] = o.WaybillNumber
	o.fieldMap["tax_amount"] = o.TaxAmount
	o.fieldMap["order_end"] = o.OrderEnd
	o.fieldMap["order_number"] = o.OrderNumber
	o.fieldMap["tax_infos"] = o.TaxInfos
	o.fieldMap["shipped_date"] = o.ShippedDate
	o.fieldMap["completed_date"] = o.CompletedDate
	o.fieldMap["note"] = o.Note
	o.fieldMap["received_date"] = o.ReceivedDate
	o.fieldMap["sales_amount"] = o.SalesAmount
	o.fieldMap["sales_info"] = o.SalesInfo
	o.fieldMap["is_international"] = o.IsInternational
}

func (o orderDetail) clone(db *gorm.DB) orderDetail {
	o.orderDetailDo.ReplaceConnPool(db.Statement.ConnPool)
	return o
}

func (o orderDetail) replaceDB(db *gorm.DB) orderDetail {
	o.orderDetailDo.ReplaceDB(db)
	return o
}

type orderDetailDo struct{ gen.DO }

type IOrderDetailDo interface {
	gen.SubQuery
	Debug() IOrderDetailDo
	WithContext(ctx context.Context) IOrderDetailDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IOrderDetailDo
	WriteDB() IOrderDetailDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IOrderDetailDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IOrderDetailDo
	Not(conds ...gen.Condition) IOrderDetailDo
	Or(conds ...gen.Condition) IOrderDetailDo
	Select(conds ...field.Expr) IOrderDetailDo
	Where(conds ...gen.Condition) IOrderDetailDo
	Order(conds ...field.Expr) IOrderDetailDo
	Distinct(cols ...field.Expr) IOrderDetailDo
	Omit(cols ...field.Expr) IOrderDetailDo
	Join(table schema.Tabler, on ...field.Expr) IOrderDetailDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IOrderDetailDo
	RightJoin(table schema.Tabler, on ...field.Expr) IOrderDetailDo
	Group(cols ...field.Expr) IOrderDetailDo
	Having(conds ...gen.Condition) IOrderDetailDo
	Limit(limit int) IOrderDetailDo
	Offset(offset int) IOrderDetailDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IOrderDetailDo
	Unscoped() IOrderDetailDo
	Create(values ...*entities.OrderDetail) error
	CreateInBatches(values []*entities.OrderDetail, batchSize int) error
	Save(values ...*entities.OrderDetail) error
	First() (*entities.OrderDetail, error)
	Take() (*entities.OrderDetail, error)
	Last() (*entities.OrderDetail, error)
	Find() ([]*entities.OrderDetail, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entities.OrderDetail, err error)
	FindInBatches(result *[]*entities.OrderDetail, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*entities.OrderDetail) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IOrderDetailDo
	Assign(attrs ...field.AssignExpr) IOrderDetailDo
	Joins(fields ...field.RelationField) IOrderDetailDo
	Preload(fields ...field.RelationField) IOrderDetailDo
	FirstOrInit() (*entities.OrderDetail, error)
	FirstOrCreate() (*entities.OrderDetail, error)
	FindByPage(offset int, limit int) (result []*entities.OrderDetail, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IOrderDetailDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (o orderDetailDo) Debug() IOrderDetailDo {
	return o.withDO(o.DO.Debug())
}

func (o orderDetailDo) WithContext(ctx context.Context) IOrderDetailDo {
	return o.withDO(o.DO.WithContext(ctx))
}

func (o orderDetailDo) ReadDB() IOrderDetailDo {
	return o.Clauses(dbresolver.Read)
}

func (o orderDetailDo) WriteDB() IOrderDetailDo {
	return o.Clauses(dbresolver.Write)
}

func (o orderDetailDo) Session(config *gorm.Session) IOrderDetailDo {
	return o.withDO(o.DO.Session(config))
}

func (o orderDetailDo) Clauses(conds ...clause.Expression) IOrderDetailDo {
	return o.withDO(o.DO.Clauses(conds...))
}

func (o orderDetailDo) Returning(value interface{}, columns ...string) IOrderDetailDo {
	return o.withDO(o.DO.Returning(value, columns...))
}

func (o orderDetailDo) Not(conds ...gen.Condition) IOrderDetailDo {
	return o.withDO(o.DO.Not(conds...))
}

func (o orderDetailDo) Or(conds ...gen.Condition) IOrderDetailDo {
	return o.withDO(o.DO.Or(conds...))
}

func (o orderDetailDo) Select(conds ...field.Expr) IOrderDetailDo {
	return o.withDO(o.DO.Select(conds...))
}

func (o orderDetailDo) Where(conds ...gen.Condition) IOrderDetailDo {
	return o.withDO(o.DO.Where(conds...))
}

func (o orderDetailDo) Order(conds ...field.Expr) IOrderDetailDo {
	return o.withDO(o.DO.Order(conds...))
}

func (o orderDetailDo) Distinct(cols ...field.Expr) IOrderDetailDo {
	return o.withDO(o.DO.Distinct(cols...))
}

func (o orderDetailDo) Omit(cols ...field.Expr) IOrderDetailDo {
	return o.withDO(o.DO.Omit(cols...))
}

func (o orderDetailDo) Join(table schema.Tabler, on ...field.Expr) IOrderDetailDo {
	return o.withDO(o.DO.Join(table, on...))
}

func (o orderDetailDo) LeftJoin(table schema.Tabler, on ...field.Expr) IOrderDetailDo {
	return o.withDO(o.DO.LeftJoin(table, on...))
}

func (o orderDetailDo) RightJoin(table schema.Tabler, on ...field.Expr) IOrderDetailDo {
	return o.withDO(o.DO.RightJoin(table, on...))
}

func (o orderDetailDo) Group(cols ...field.Expr) IOrderDetailDo {
	return o.withDO(o.DO.Group(cols...))
}

func (o orderDetailDo) Having(conds ...gen.Condition) IOrderDetailDo {
	return o.withDO(o.DO.Having(conds...))
}

func (o orderDetailDo) Limit(limit int) IOrderDetailDo {
	return o.withDO(o.DO.Limit(limit))
}

func (o orderDetailDo) Offset(offset int) IOrderDetailDo {
	return o.withDO(o.DO.Offset(offset))
}

func (o orderDetailDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IOrderDetailDo {
	return o.withDO(o.DO.Scopes(funcs...))
}

func (o orderDetailDo) Unscoped() IOrderDetailDo {
	return o.withDO(o.DO.Unscoped())
}

func (o orderDetailDo) Create(values ...*entities.OrderDetail) error {
	if len(values) == 0 {
		return nil
	}
	return o.DO.Create(values)
}

func (o orderDetailDo) CreateInBatches(values []*entities.OrderDetail, batchSize int) error {
	return o.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (o orderDetailDo) Save(values ...*entities.OrderDetail) error {
	if len(values) == 0 {
		return nil
	}
	return o.DO.Save(values)
}

func (o orderDetailDo) First() (*entities.OrderDetail, error) {
	if result, err := o.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entities.OrderDetail), nil
	}
}

func (o orderDetailDo) Take() (*entities.OrderDetail, error) {
	if result, err := o.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entities.OrderDetail), nil
	}
}

func (o orderDetailDo) Last() (*entities.OrderDetail, error) {
	if result, err := o.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entities.OrderDetail), nil
	}
}

func (o orderDetailDo) Find() ([]*entities.OrderDetail, error) {
	result, err := o.DO.Find()
	return result.([]*entities.OrderDetail), err
}

func (o orderDetailDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entities.OrderDetail, err error) {
	buf := make([]*entities.OrderDetail, 0, batchSize)
	err = o.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (o orderDetailDo) FindInBatches(result *[]*entities.OrderDetail, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return o.DO.FindInBatches(result, batchSize, fc)
}

func (o orderDetailDo) Attrs(attrs ...field.AssignExpr) IOrderDetailDo {
	return o.withDO(o.DO.Attrs(attrs...))
}

func (o orderDetailDo) Assign(attrs ...field.AssignExpr) IOrderDetailDo {
	return o.withDO(o.DO.Assign(attrs...))
}

func (o orderDetailDo) Joins(fields ...field.RelationField) IOrderDetailDo {
	for _, _f := range fields {
		o = *o.withDO(o.DO.Joins(_f))
	}
	return &o
}

func (o orderDetailDo) Preload(fields ...field.RelationField) IOrderDetailDo {
	for _, _f := range fields {
		o = *o.withDO(o.DO.Preload(_f))
	}
	return &o
}

func (o orderDetailDo) FirstOrInit() (*entities.OrderDetail, error) {
	if result, err := o.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entities.OrderDetail), nil
	}
}

func (o orderDetailDo) FirstOrCreate() (*entities.OrderDetail, error) {
	if result, err := o.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entities.OrderDetail), nil
	}
}

func (o orderDetailDo) FindByPage(offset int, limit int) (result []*entities.OrderDetail, count int64, err error) {
	result, err = o.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = o.Offset(-1).Limit(-1).Count()
	return
}

func (o orderDetailDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = o.Count()
	if err != nil {
		return
	}

	err = o.Offset(offset).Limit(limit).Scan(result)
	return
}

func (o orderDetailDo) Scan(result interface{}) (err error) {
	return o.DO.Scan(result)
}

func (o orderDetailDo) Delete(models ...*entities.OrderDetail) (result gen.ResultInfo, err error) {
	return o.DO.Delete(models)
}

func (o *orderDetailDo) withDO(do gen.Dao) *orderDetailDo {
	o.DO = *do.(*gen.DO)
	return o
}
