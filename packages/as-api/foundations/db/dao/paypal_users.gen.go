// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"as-api/as/foundations/db/entities"
)

func newPaypalUser(db *gorm.DB, opts ...gen.DOOption) paypalUser {
	_paypalUser := paypalUser{}

	_paypalUser.paypalUserDo.UseDB(db, opts...)
	_paypalUser.paypalUserDo.UseModel(&entities.PaypalUser{})

	tableName := _paypalUser.paypalUserDo.TableName()
	_paypalUser.ALL = field.NewAsterisk(tableName)
	_paypalUser.ID = field.NewString(tableName, "id")
	_paypalUser.CreatedAt = field.NewTime(tableName, "created_at")
	_paypalUser.UpdatedAt = field.NewTime(tableName, "updated_at")
	_paypalUser.DeletedAt = field.NewField(tableName, "deleted_at")
	_paypalUser.CustomerID = field.NewString(tableName, "customer_id")
	_paypalUser.VaultCardID = field.NewString(tableName, "vault_card_id")
	_paypalUser.CardInfo = field.NewField(tableName, "card_info")

	_paypalUser.fillFieldMap()

	return _paypalUser
}

type paypalUser struct {
	paypalUserDo

	ALL         field.Asterisk
	ID          field.String
	CreatedAt   field.Time
	UpdatedAt   field.Time
	DeletedAt   field.Field
	CustomerID  field.String
	VaultCardID field.String
	CardInfo    field.Field

	fieldMap map[string]field.Expr
}

func (p paypalUser) Table(newTableName string) *paypalUser {
	p.paypalUserDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p paypalUser) As(alias string) *paypalUser {
	p.paypalUserDo.DO = *(p.paypalUserDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *paypalUser) updateTableName(table string) *paypalUser {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewString(table, "id")
	p.CreatedAt = field.NewTime(table, "created_at")
	p.UpdatedAt = field.NewTime(table, "updated_at")
	p.DeletedAt = field.NewField(table, "deleted_at")
	p.CustomerID = field.NewString(table, "customer_id")
	p.VaultCardID = field.NewString(table, "vault_card_id")
	p.CardInfo = field.NewField(table, "card_info")

	p.fillFieldMap()

	return p
}

func (p *paypalUser) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *paypalUser) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 7)
	p.fieldMap["id"] = p.ID
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
	p.fieldMap["deleted_at"] = p.DeletedAt
	p.fieldMap["customer_id"] = p.CustomerID
	p.fieldMap["vault_card_id"] = p.VaultCardID
	p.fieldMap["card_info"] = p.CardInfo
}

func (p paypalUser) clone(db *gorm.DB) paypalUser {
	p.paypalUserDo.ReplaceConnPool(db.Statement.ConnPool)
	return p
}

func (p paypalUser) replaceDB(db *gorm.DB) paypalUser {
	p.paypalUserDo.ReplaceDB(db)
	return p
}

type paypalUserDo struct{ gen.DO }

type IPaypalUserDo interface {
	gen.SubQuery
	Debug() IPaypalUserDo
	WithContext(ctx context.Context) IPaypalUserDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IPaypalUserDo
	WriteDB() IPaypalUserDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IPaypalUserDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IPaypalUserDo
	Not(conds ...gen.Condition) IPaypalUserDo
	Or(conds ...gen.Condition) IPaypalUserDo
	Select(conds ...field.Expr) IPaypalUserDo
	Where(conds ...gen.Condition) IPaypalUserDo
	Order(conds ...field.Expr) IPaypalUserDo
	Distinct(cols ...field.Expr) IPaypalUserDo
	Omit(cols ...field.Expr) IPaypalUserDo
	Join(table schema.Tabler, on ...field.Expr) IPaypalUserDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IPaypalUserDo
	RightJoin(table schema.Tabler, on ...field.Expr) IPaypalUserDo
	Group(cols ...field.Expr) IPaypalUserDo
	Having(conds ...gen.Condition) IPaypalUserDo
	Limit(limit int) IPaypalUserDo
	Offset(offset int) IPaypalUserDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IPaypalUserDo
	Unscoped() IPaypalUserDo
	Create(values ...*entities.PaypalUser) error
	CreateInBatches(values []*entities.PaypalUser, batchSize int) error
	Save(values ...*entities.PaypalUser) error
	First() (*entities.PaypalUser, error)
	Take() (*entities.PaypalUser, error)
	Last() (*entities.PaypalUser, error)
	Find() ([]*entities.PaypalUser, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entities.PaypalUser, err error)
	FindInBatches(result *[]*entities.PaypalUser, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*entities.PaypalUser) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IPaypalUserDo
	Assign(attrs ...field.AssignExpr) IPaypalUserDo
	Joins(fields ...field.RelationField) IPaypalUserDo
	Preload(fields ...field.RelationField) IPaypalUserDo
	FirstOrInit() (*entities.PaypalUser, error)
	FirstOrCreate() (*entities.PaypalUser, error)
	FindByPage(offset int, limit int) (result []*entities.PaypalUser, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IPaypalUserDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (p paypalUserDo) Debug() IPaypalUserDo {
	return p.withDO(p.DO.Debug())
}

func (p paypalUserDo) WithContext(ctx context.Context) IPaypalUserDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p paypalUserDo) ReadDB() IPaypalUserDo {
	return p.Clauses(dbresolver.Read)
}

func (p paypalUserDo) WriteDB() IPaypalUserDo {
	return p.Clauses(dbresolver.Write)
}

func (p paypalUserDo) Session(config *gorm.Session) IPaypalUserDo {
	return p.withDO(p.DO.Session(config))
}

func (p paypalUserDo) Clauses(conds ...clause.Expression) IPaypalUserDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p paypalUserDo) Returning(value interface{}, columns ...string) IPaypalUserDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p paypalUserDo) Not(conds ...gen.Condition) IPaypalUserDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p paypalUserDo) Or(conds ...gen.Condition) IPaypalUserDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p paypalUserDo) Select(conds ...field.Expr) IPaypalUserDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p paypalUserDo) Where(conds ...gen.Condition) IPaypalUserDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p paypalUserDo) Order(conds ...field.Expr) IPaypalUserDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p paypalUserDo) Distinct(cols ...field.Expr) IPaypalUserDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p paypalUserDo) Omit(cols ...field.Expr) IPaypalUserDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p paypalUserDo) Join(table schema.Tabler, on ...field.Expr) IPaypalUserDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p paypalUserDo) LeftJoin(table schema.Tabler, on ...field.Expr) IPaypalUserDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p paypalUserDo) RightJoin(table schema.Tabler, on ...field.Expr) IPaypalUserDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p paypalUserDo) Group(cols ...field.Expr) IPaypalUserDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p paypalUserDo) Having(conds ...gen.Condition) IPaypalUserDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p paypalUserDo) Limit(limit int) IPaypalUserDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p paypalUserDo) Offset(offset int) IPaypalUserDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p paypalUserDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IPaypalUserDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p paypalUserDo) Unscoped() IPaypalUserDo {
	return p.withDO(p.DO.Unscoped())
}

func (p paypalUserDo) Create(values ...*entities.PaypalUser) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p paypalUserDo) CreateInBatches(values []*entities.PaypalUser, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p paypalUserDo) Save(values ...*entities.PaypalUser) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p paypalUserDo) First() (*entities.PaypalUser, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entities.PaypalUser), nil
	}
}

func (p paypalUserDo) Take() (*entities.PaypalUser, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entities.PaypalUser), nil
	}
}

func (p paypalUserDo) Last() (*entities.PaypalUser, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entities.PaypalUser), nil
	}
}

func (p paypalUserDo) Find() ([]*entities.PaypalUser, error) {
	result, err := p.DO.Find()
	return result.([]*entities.PaypalUser), err
}

func (p paypalUserDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entities.PaypalUser, err error) {
	buf := make([]*entities.PaypalUser, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p paypalUserDo) FindInBatches(result *[]*entities.PaypalUser, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p paypalUserDo) Attrs(attrs ...field.AssignExpr) IPaypalUserDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p paypalUserDo) Assign(attrs ...field.AssignExpr) IPaypalUserDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p paypalUserDo) Joins(fields ...field.RelationField) IPaypalUserDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p paypalUserDo) Preload(fields ...field.RelationField) IPaypalUserDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p paypalUserDo) FirstOrInit() (*entities.PaypalUser, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entities.PaypalUser), nil
	}
}

func (p paypalUserDo) FirstOrCreate() (*entities.PaypalUser, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entities.PaypalUser), nil
	}
}

func (p paypalUserDo) FindByPage(offset int, limit int) (result []*entities.PaypalUser, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p paypalUserDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p paypalUserDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p paypalUserDo) Delete(models ...*entities.PaypalUser) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *paypalUserDo) withDO(do gen.Dao) *paypalUserDo {
	p.DO = *do.(*gen.DO)
	return p
}
