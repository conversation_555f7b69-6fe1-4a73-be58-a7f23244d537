// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"as-api/as/foundations/db/entities"
)

func newAuth(db *gorm.DB, opts ...gen.DOOption) auth {
	_auth := auth{}

	_auth.authDo.UseDB(db, opts...)
	_auth.authDo.UseModel(&entities.Auth{})

	tableName := _auth.authDo.TableName()
	_auth.ALL = field.NewAsterisk(tableName)
	_auth.ID = field.NewString(tableName, "id")
	_auth.CreatedAt = field.NewTime(tableName, "created_at")
	_auth.UpdatedAt = field.NewTime(tableName, "updated_at")
	_auth.DeletedAt = field.NewField(tableName, "deleted_at")
	_auth.UserID = field.NewString(tableName, "user_id")
	_auth.AdminID = field.NewString(tableName, "admin_id")
	_auth.Username = field.NewString(tableName, "username")
	_auth.Password = field.NewString(tableName, "password")
	_auth.AccountID = field.NewString(tableName, "account_id")

	_auth.fillFieldMap()

	return _auth
}

type auth struct {
	authDo

	ALL       field.Asterisk
	ID        field.String
	CreatedAt field.Time
	UpdatedAt field.Time
	DeletedAt field.Field
	UserID    field.String
	AdminID   field.String
	Username  field.String
	Password  field.String
	AccountID field.String

	fieldMap map[string]field.Expr
}

func (a auth) Table(newTableName string) *auth {
	a.authDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a auth) As(alias string) *auth {
	a.authDo.DO = *(a.authDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *auth) updateTableName(table string) *auth {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewString(table, "id")
	a.CreatedAt = field.NewTime(table, "created_at")
	a.UpdatedAt = field.NewTime(table, "updated_at")
	a.DeletedAt = field.NewField(table, "deleted_at")
	a.UserID = field.NewString(table, "user_id")
	a.AdminID = field.NewString(table, "admin_id")
	a.Username = field.NewString(table, "username")
	a.Password = field.NewString(table, "password")
	a.AccountID = field.NewString(table, "account_id")

	a.fillFieldMap()

	return a
}

func (a *auth) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *auth) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 9)
	a.fieldMap["id"] = a.ID
	a.fieldMap["created_at"] = a.CreatedAt
	a.fieldMap["updated_at"] = a.UpdatedAt
	a.fieldMap["deleted_at"] = a.DeletedAt
	a.fieldMap["user_id"] = a.UserID
	a.fieldMap["admin_id"] = a.AdminID
	a.fieldMap["username"] = a.Username
	a.fieldMap["password"] = a.Password
	a.fieldMap["account_id"] = a.AccountID
}

func (a auth) clone(db *gorm.DB) auth {
	a.authDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a auth) replaceDB(db *gorm.DB) auth {
	a.authDo.ReplaceDB(db)
	return a
}

type authDo struct{ gen.DO }

type IAuthDo interface {
	gen.SubQuery
	Debug() IAuthDo
	WithContext(ctx context.Context) IAuthDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IAuthDo
	WriteDB() IAuthDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IAuthDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IAuthDo
	Not(conds ...gen.Condition) IAuthDo
	Or(conds ...gen.Condition) IAuthDo
	Select(conds ...field.Expr) IAuthDo
	Where(conds ...gen.Condition) IAuthDo
	Order(conds ...field.Expr) IAuthDo
	Distinct(cols ...field.Expr) IAuthDo
	Omit(cols ...field.Expr) IAuthDo
	Join(table schema.Tabler, on ...field.Expr) IAuthDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IAuthDo
	RightJoin(table schema.Tabler, on ...field.Expr) IAuthDo
	Group(cols ...field.Expr) IAuthDo
	Having(conds ...gen.Condition) IAuthDo
	Limit(limit int) IAuthDo
	Offset(offset int) IAuthDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IAuthDo
	Unscoped() IAuthDo
	Create(values ...*entities.Auth) error
	CreateInBatches(values []*entities.Auth, batchSize int) error
	Save(values ...*entities.Auth) error
	First() (*entities.Auth, error)
	Take() (*entities.Auth, error)
	Last() (*entities.Auth, error)
	Find() ([]*entities.Auth, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entities.Auth, err error)
	FindInBatches(result *[]*entities.Auth, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*entities.Auth) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IAuthDo
	Assign(attrs ...field.AssignExpr) IAuthDo
	Joins(fields ...field.RelationField) IAuthDo
	Preload(fields ...field.RelationField) IAuthDo
	FirstOrInit() (*entities.Auth, error)
	FirstOrCreate() (*entities.Auth, error)
	FindByPage(offset int, limit int) (result []*entities.Auth, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IAuthDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a authDo) Debug() IAuthDo {
	return a.withDO(a.DO.Debug())
}

func (a authDo) WithContext(ctx context.Context) IAuthDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a authDo) ReadDB() IAuthDo {
	return a.Clauses(dbresolver.Read)
}

func (a authDo) WriteDB() IAuthDo {
	return a.Clauses(dbresolver.Write)
}

func (a authDo) Session(config *gorm.Session) IAuthDo {
	return a.withDO(a.DO.Session(config))
}

func (a authDo) Clauses(conds ...clause.Expression) IAuthDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a authDo) Returning(value interface{}, columns ...string) IAuthDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a authDo) Not(conds ...gen.Condition) IAuthDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a authDo) Or(conds ...gen.Condition) IAuthDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a authDo) Select(conds ...field.Expr) IAuthDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a authDo) Where(conds ...gen.Condition) IAuthDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a authDo) Order(conds ...field.Expr) IAuthDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a authDo) Distinct(cols ...field.Expr) IAuthDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a authDo) Omit(cols ...field.Expr) IAuthDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a authDo) Join(table schema.Tabler, on ...field.Expr) IAuthDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a authDo) LeftJoin(table schema.Tabler, on ...field.Expr) IAuthDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a authDo) RightJoin(table schema.Tabler, on ...field.Expr) IAuthDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a authDo) Group(cols ...field.Expr) IAuthDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a authDo) Having(conds ...gen.Condition) IAuthDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a authDo) Limit(limit int) IAuthDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a authDo) Offset(offset int) IAuthDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a authDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IAuthDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a authDo) Unscoped() IAuthDo {
	return a.withDO(a.DO.Unscoped())
}

func (a authDo) Create(values ...*entities.Auth) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a authDo) CreateInBatches(values []*entities.Auth, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a authDo) Save(values ...*entities.Auth) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a authDo) First() (*entities.Auth, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entities.Auth), nil
	}
}

func (a authDo) Take() (*entities.Auth, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entities.Auth), nil
	}
}

func (a authDo) Last() (*entities.Auth, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entities.Auth), nil
	}
}

func (a authDo) Find() ([]*entities.Auth, error) {
	result, err := a.DO.Find()
	return result.([]*entities.Auth), err
}

func (a authDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entities.Auth, err error) {
	buf := make([]*entities.Auth, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a authDo) FindInBatches(result *[]*entities.Auth, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a authDo) Attrs(attrs ...field.AssignExpr) IAuthDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a authDo) Assign(attrs ...field.AssignExpr) IAuthDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a authDo) Joins(fields ...field.RelationField) IAuthDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a authDo) Preload(fields ...field.RelationField) IAuthDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a authDo) FirstOrInit() (*entities.Auth, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entities.Auth), nil
	}
}

func (a authDo) FirstOrCreate() (*entities.Auth, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entities.Auth), nil
	}
}

func (a authDo) FindByPage(offset int, limit int) (result []*entities.Auth, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a authDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a authDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a authDo) Delete(models ...*entities.Auth) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *authDo) withDO(do gen.Dao) *authDo {
	a.DO = *do.(*gen.DO)
	return a
}
