// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"as-api/as/foundations/db/entities"
)

func newMaintenanceSchedule(db *gorm.DB, opts ...gen.DOOption) maintenanceSchedule {
	_maintenanceSchedule := maintenanceSchedule{}

	_maintenanceSchedule.maintenanceScheduleDo.UseDB(db, opts...)
	_maintenanceSchedule.maintenanceScheduleDo.UseModel(&entities.MaintenanceSchedule{})

	tableName := _maintenanceSchedule.maintenanceScheduleDo.TableName()
	_maintenanceSchedule.ALL = field.NewAsterisk(tableName)
	_maintenanceSchedule.ID = field.NewString(tableName, "id")
	_maintenanceSchedule.CreatedAt = field.NewTime(tableName, "created_at")
	_maintenanceSchedule.UpdatedAt = field.NewTime(tableName, "updated_at")
	_maintenanceSchedule.DeletedAt = field.NewField(tableName, "deleted_at")
	_maintenanceSchedule.StartTime = field.NewTime(tableName, "start_time")
	_maintenanceSchedule.EndTime = field.NewTime(tableName, "end_time")
	_maintenanceSchedule.Status = field.NewString(tableName, "status")
	_maintenanceSchedule.Description = field.NewField(tableName, "description")

	_maintenanceSchedule.fillFieldMap()

	return _maintenanceSchedule
}

type maintenanceSchedule struct {
	maintenanceScheduleDo

	ALL         field.Asterisk
	ID          field.String
	CreatedAt   field.Time
	UpdatedAt   field.Time
	DeletedAt   field.Field
	StartTime   field.Time
	EndTime     field.Time // NULL when maintenance is in_progress, timestamp when completed
	Status      field.String
	Description field.Field

	fieldMap map[string]field.Expr
}

func (m maintenanceSchedule) Table(newTableName string) *maintenanceSchedule {
	m.maintenanceScheduleDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m maintenanceSchedule) As(alias string) *maintenanceSchedule {
	m.maintenanceScheduleDo.DO = *(m.maintenanceScheduleDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *maintenanceSchedule) updateTableName(table string) *maintenanceSchedule {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewString(table, "id")
	m.CreatedAt = field.NewTime(table, "created_at")
	m.UpdatedAt = field.NewTime(table, "updated_at")
	m.DeletedAt = field.NewField(table, "deleted_at")
	m.StartTime = field.NewTime(table, "start_time")
	m.EndTime = field.NewTime(table, "end_time")
	m.Status = field.NewString(table, "status")
	m.Description = field.NewField(table, "description")

	m.fillFieldMap()

	return m
}

func (m *maintenanceSchedule) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *maintenanceSchedule) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 8)
	m.fieldMap["id"] = m.ID
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["deleted_at"] = m.DeletedAt
	m.fieldMap["start_time"] = m.StartTime
	m.fieldMap["end_time"] = m.EndTime
	m.fieldMap["status"] = m.Status
	m.fieldMap["description"] = m.Description
}

func (m maintenanceSchedule) clone(db *gorm.DB) maintenanceSchedule {
	m.maintenanceScheduleDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m maintenanceSchedule) replaceDB(db *gorm.DB) maintenanceSchedule {
	m.maintenanceScheduleDo.ReplaceDB(db)
	return m
}

type maintenanceScheduleDo struct{ gen.DO }

type IMaintenanceScheduleDo interface {
	gen.SubQuery
	Debug() IMaintenanceScheduleDo
	WithContext(ctx context.Context) IMaintenanceScheduleDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMaintenanceScheduleDo
	WriteDB() IMaintenanceScheduleDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMaintenanceScheduleDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMaintenanceScheduleDo
	Not(conds ...gen.Condition) IMaintenanceScheduleDo
	Or(conds ...gen.Condition) IMaintenanceScheduleDo
	Select(conds ...field.Expr) IMaintenanceScheduleDo
	Where(conds ...gen.Condition) IMaintenanceScheduleDo
	Order(conds ...field.Expr) IMaintenanceScheduleDo
	Distinct(cols ...field.Expr) IMaintenanceScheduleDo
	Omit(cols ...field.Expr) IMaintenanceScheduleDo
	Join(table schema.Tabler, on ...field.Expr) IMaintenanceScheduleDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMaintenanceScheduleDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMaintenanceScheduleDo
	Group(cols ...field.Expr) IMaintenanceScheduleDo
	Having(conds ...gen.Condition) IMaintenanceScheduleDo
	Limit(limit int) IMaintenanceScheduleDo
	Offset(offset int) IMaintenanceScheduleDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMaintenanceScheduleDo
	Unscoped() IMaintenanceScheduleDo
	Create(values ...*entities.MaintenanceSchedule) error
	CreateInBatches(values []*entities.MaintenanceSchedule, batchSize int) error
	Save(values ...*entities.MaintenanceSchedule) error
	First() (*entities.MaintenanceSchedule, error)
	Take() (*entities.MaintenanceSchedule, error)
	Last() (*entities.MaintenanceSchedule, error)
	Find() ([]*entities.MaintenanceSchedule, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entities.MaintenanceSchedule, err error)
	FindInBatches(result *[]*entities.MaintenanceSchedule, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*entities.MaintenanceSchedule) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMaintenanceScheduleDo
	Assign(attrs ...field.AssignExpr) IMaintenanceScheduleDo
	Joins(fields ...field.RelationField) IMaintenanceScheduleDo
	Preload(fields ...field.RelationField) IMaintenanceScheduleDo
	FirstOrInit() (*entities.MaintenanceSchedule, error)
	FirstOrCreate() (*entities.MaintenanceSchedule, error)
	FindByPage(offset int, limit int) (result []*entities.MaintenanceSchedule, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMaintenanceScheduleDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m maintenanceScheduleDo) Debug() IMaintenanceScheduleDo {
	return m.withDO(m.DO.Debug())
}

func (m maintenanceScheduleDo) WithContext(ctx context.Context) IMaintenanceScheduleDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m maintenanceScheduleDo) ReadDB() IMaintenanceScheduleDo {
	return m.Clauses(dbresolver.Read)
}

func (m maintenanceScheduleDo) WriteDB() IMaintenanceScheduleDo {
	return m.Clauses(dbresolver.Write)
}

func (m maintenanceScheduleDo) Session(config *gorm.Session) IMaintenanceScheduleDo {
	return m.withDO(m.DO.Session(config))
}

func (m maintenanceScheduleDo) Clauses(conds ...clause.Expression) IMaintenanceScheduleDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m maintenanceScheduleDo) Returning(value interface{}, columns ...string) IMaintenanceScheduleDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m maintenanceScheduleDo) Not(conds ...gen.Condition) IMaintenanceScheduleDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m maintenanceScheduleDo) Or(conds ...gen.Condition) IMaintenanceScheduleDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m maintenanceScheduleDo) Select(conds ...field.Expr) IMaintenanceScheduleDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m maintenanceScheduleDo) Where(conds ...gen.Condition) IMaintenanceScheduleDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m maintenanceScheduleDo) Order(conds ...field.Expr) IMaintenanceScheduleDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m maintenanceScheduleDo) Distinct(cols ...field.Expr) IMaintenanceScheduleDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m maintenanceScheduleDo) Omit(cols ...field.Expr) IMaintenanceScheduleDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m maintenanceScheduleDo) Join(table schema.Tabler, on ...field.Expr) IMaintenanceScheduleDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m maintenanceScheduleDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMaintenanceScheduleDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m maintenanceScheduleDo) RightJoin(table schema.Tabler, on ...field.Expr) IMaintenanceScheduleDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m maintenanceScheduleDo) Group(cols ...field.Expr) IMaintenanceScheduleDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m maintenanceScheduleDo) Having(conds ...gen.Condition) IMaintenanceScheduleDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m maintenanceScheduleDo) Limit(limit int) IMaintenanceScheduleDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m maintenanceScheduleDo) Offset(offset int) IMaintenanceScheduleDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m maintenanceScheduleDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMaintenanceScheduleDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m maintenanceScheduleDo) Unscoped() IMaintenanceScheduleDo {
	return m.withDO(m.DO.Unscoped())
}

func (m maintenanceScheduleDo) Create(values ...*entities.MaintenanceSchedule) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m maintenanceScheduleDo) CreateInBatches(values []*entities.MaintenanceSchedule, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m maintenanceScheduleDo) Save(values ...*entities.MaintenanceSchedule) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m maintenanceScheduleDo) First() (*entities.MaintenanceSchedule, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entities.MaintenanceSchedule), nil
	}
}

func (m maintenanceScheduleDo) Take() (*entities.MaintenanceSchedule, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entities.MaintenanceSchedule), nil
	}
}

func (m maintenanceScheduleDo) Last() (*entities.MaintenanceSchedule, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entities.MaintenanceSchedule), nil
	}
}

func (m maintenanceScheduleDo) Find() ([]*entities.MaintenanceSchedule, error) {
	result, err := m.DO.Find()
	return result.([]*entities.MaintenanceSchedule), err
}

func (m maintenanceScheduleDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entities.MaintenanceSchedule, err error) {
	buf := make([]*entities.MaintenanceSchedule, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m maintenanceScheduleDo) FindInBatches(result *[]*entities.MaintenanceSchedule, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m maintenanceScheduleDo) Attrs(attrs ...field.AssignExpr) IMaintenanceScheduleDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m maintenanceScheduleDo) Assign(attrs ...field.AssignExpr) IMaintenanceScheduleDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m maintenanceScheduleDo) Joins(fields ...field.RelationField) IMaintenanceScheduleDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m maintenanceScheduleDo) Preload(fields ...field.RelationField) IMaintenanceScheduleDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m maintenanceScheduleDo) FirstOrInit() (*entities.MaintenanceSchedule, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entities.MaintenanceSchedule), nil
	}
}

func (m maintenanceScheduleDo) FirstOrCreate() (*entities.MaintenanceSchedule, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entities.MaintenanceSchedule), nil
	}
}

func (m maintenanceScheduleDo) FindByPage(offset int, limit int) (result []*entities.MaintenanceSchedule, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m maintenanceScheduleDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m maintenanceScheduleDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m maintenanceScheduleDo) Delete(models ...*entities.MaintenanceSchedule) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *maintenanceScheduleDo) withDO(do gen.Dao) *maintenanceScheduleDo {
	m.DO = *do.(*gen.DO)
	return m
}
