// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entities

import (
	"time"
)

const TableNameOrderDetail = "order_details"

// OrderDetail mapped from table <order_details>
type OrderDetail struct {
	ID                *string    `gorm:"column:id;type:uuid;primaryKey;default:uuid_generate_v4()" json:"id"`
	CreatedAt         *time.Time `gorm:"column:created_at;type:timestamp without time zone;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt         *time.Time `gorm:"column:updated_at;type:timestamp without time zone;default:CURRENT_TIMESTAMP" json:"updated_at"`
	OrderID           string     `gorm:"column:order_id;type:uuid;not null" json:"order_id"`
	ProductID         string     `gorm:"column:product_id;type:uuid;not null" json:"product_id"`
	Price             float64    `gorm:"column:price;type:numeric(12,2);not null" json:"price"`
	TransactionStatus string     `gorm:"column:transaction_status;type:character varying(50);not null" json:"transaction_status"`
	TaxRate           float64    `gorm:"column:tax_rate;type:numeric(5,2);not null" json:"tax_rate"`
	DiscountAmount    *float64   `gorm:"column:discount_amount;type:numeric(12,2);default:0.00" json:"discount_amount"`
	TotalPrice        *float64   `gorm:"column:total_price;type:numeric(12,2)" json:"total_price"`
	WaybillNumber     *string    `gorm:"column:waybill_number;type:character varying(255)" json:"waybill_number"`
	TaxAmount         *float64   `gorm:"column:tax_amount;type:numeric(12,2)" json:"tax_amount"`
	OrderEnd          *time.Time `gorm:"column:order_end;type:timestamp without time zone" json:"order_end"`
	OrderNumber       *string    `gorm:"column:order_number;type:character varying(255)" json:"order_number"`
	TaxInfos          *JSON      `gorm:"column:tax_infos;type:jsonb" json:"tax_infos"`
	ShippedDate       *time.Time `gorm:"column:shipped_date;type:timestamp without time zone" json:"shipped_date"`
	CompletedDate     *time.Time `gorm:"column:completed_date;type:timestamp without time zone" json:"completed_date"`
	Note              *string    `gorm:"column:note;type:character varying(255)" json:"note"`
	ReceivedDate      *time.Time `gorm:"column:received_date;type:timestamp without time zone" json:"received_date"`
	SalesAmount       *float64   `gorm:"column:sales_amount;type:numeric(12,2)" json:"sales_amount"`
	SalesInfo         *JSON      `gorm:"column:sales_info;type:jsonb" json:"sales_info"`
	IsInternational   *bool      `gorm:"column:is_international;type:boolean" json:"is_international"`
}

// TableName OrderDetail's table name
func (*OrderDetail) TableName() string {
	return TableNameOrderDetail
}
