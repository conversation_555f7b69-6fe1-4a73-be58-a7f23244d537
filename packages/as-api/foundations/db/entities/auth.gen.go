// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entities

import (
	"time"

	"gorm.io/gorm"
)

const TableNameAuth = "auth"

// Auth mapped from table <auth>
type Auth struct {
	ID        *string        `gorm:"column:id;type:uuid;primaryKey;default:uuid_generate_v4()" json:"id"`
	CreatedAt *time.Time     `gorm:"column:created_at;type:timestamp without time zone;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt *time.Time     `gorm:"column:updated_at;type:timestamp without time zone;not null;default:CURRENT_TIMESTAMP" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at;type:timestamp without time zone" json:"deleted_at"`
	UserID    *string        `gorm:"column:user_id;type:uuid" json:"user_id"`
	AdminID   *string        `gorm:"column:admin_id;type:uuid" json:"admin_id"`
	Username  string         `gorm:"column:username;type:character varying(255);not null" json:"username"`
	Password  string         `gorm:"column:password;type:character varying(255);not null" json:"password"`
	AccountID *string        `gorm:"column:account_id;type:character varying(255)" json:"account_id"`
}

// TableName Auth's table name
func (*Auth) TableName() string {
	return TableNameAuth
}
