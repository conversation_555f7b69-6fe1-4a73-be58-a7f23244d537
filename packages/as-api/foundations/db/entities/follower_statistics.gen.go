// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entities

import (
	"time"
)

const TableNameFollowerStatistic = "follower_statistics"

// FollowerStatistic mapped from table <follower_statistics>
type FollowerStatistic struct {
	ID            *string    `gorm:"column:id;type:uuid;primaryKey;default:uuid_generate_v4()" json:"id"`
	CreatedAt     *time.Time `gorm:"column:created_at;type:timestamp without time zone;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt     *time.Time `gorm:"column:updated_at;type:timestamp without time zone;default:CURRENT_TIMESTAMP" json:"updated_at"`
	SellerID      string     `gorm:"column:seller_id;type:uuid;not null" json:"seller_id"`
	CountryCode   string     `gorm:"column:country_code;type:character varying(3);not null;comment:ISO 3166-1 alpha-3 country code (e.g., USA, JPN, GBR) - primary country identifier" json:"country_code"` // ISO 3166-1 alpha-3 country code (e.g., USA, JPN, GBR) - primary country identifier
	Gender        string     `gorm:"column:gender;type:character varying(20);not null;comment:Follower gender: male, female, or other" json:"gender"`                                                       // Follower gender: male, female, or other
	AgeGroup      string     `gorm:"column:age_group;type:character varying(20);not null;comment:Age group ranges: under-20, 20-29, 30-39, 40-49, 50-59, 60-and-over" json:"age_group"`                     // Age group ranges: under-20, 20-29, 30-39, 40-49, 50-59, 60-and-over
	FollowerCount *int32     `gorm:"column:follower_count;type:integer;comment:Number of followers in this demographic segment" json:"follower_count"`                                                      // Number of followers in this demographic segment
	CalculatedAt  *time.Time `gorm:"column:calculated_at;type:timestamp without time zone;default:CURRENT_TIMESTAMP;comment:Timestamp when this statistic was last calculated" json:"calculated_at"`        // Timestamp when this statistic was last calculated
}

// TableName FollowerStatistic's table name
func (*FollowerStatistic) TableName() string {
	return TableNameFollowerStatistic
}
