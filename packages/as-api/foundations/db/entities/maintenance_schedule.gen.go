// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entities

import (
	"time"

	"gorm.io/gorm"
)

const TableNameMaintenanceSchedule = "maintenance_schedule"

// MaintenanceSchedule mapped from table <maintenance_schedule>
type MaintenanceSchedule struct {
	ID          *string        `gorm:"column:id;type:uuid;primaryKey;default:uuid_generate_v4()" json:"id"`
	CreatedAt   *time.Time     `gorm:"column:created_at;type:timestamp without time zone;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt   *time.Time     `gorm:"column:updated_at;type:timestamp without time zone;not null;default:CURRENT_TIMESTAMP" json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"column:deleted_at;type:timestamp without time zone" json:"deleted_at"`
	StartTime   time.Time      `gorm:"column:start_time;type:timestamp without time zone;not null" json:"start_time"`
	EndTime     *time.Time     `gorm:"column:end_time;type:timestamp without time zone;comment:NULL when maintenance is in_progress, timestamp when completed" json:"end_time"` // NULL when maintenance is in_progress, timestamp when completed
	Status      *string        `gorm:"column:status;type:character varying(20);not null;default:scheduled" json:"status"`
	Description *JSON          `gorm:"column:description;type:jsonb" json:"description"`
}

// TableName MaintenanceSchedule's table name
func (*MaintenanceSchedule) TableName() string {
	return TableNameMaintenanceSchedule
}
