// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entities

import (
	"time"

	"gorm.io/gorm"
)

const TableNamePaypalUser = "paypal_users"

// PaypalUser mapped from table <paypal_users>
type PaypalUser struct {
	ID          *string        `gorm:"column:id;type:uuid;primaryKey;default:uuid_generate_v4()" json:"id"`
	CreatedAt   *time.Time     `gorm:"column:created_at;type:timestamp without time zone;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt   *time.Time     `gorm:"column:updated_at;type:timestamp without time zone;default:CURRENT_TIMESTAMP" json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"column:deleted_at;type:timestamp without time zone" json:"deleted_at"`
	CustomerID  string         `gorm:"column:customer_id;type:character varying(255);not null" json:"customer_id"`
	VaultCardID string         `gorm:"column:vault_card_id;type:character varying(255);not null" json:"vault_card_id"`
	CardInfo    *JSON          `gorm:"column:card_info;type:jsonb" json:"card_info"`
}

// TableName PaypalUser's table name
func (*PaypalUser) TableName() string {
	return TableNamePaypalUser
}
