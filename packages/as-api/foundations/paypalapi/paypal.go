package paypalapi

import (
	"context"
	"strconv"
	"time"

	"as-api/as/foundations/logger"
	checkoutorders "as-api/as/foundations/paypalapi/checkout-orders"
	paymenttokens "as-api/as/foundations/paypalapi/payment-tokens"

	"github.com/pkg/errors"
)

type PayPalConfig struct {
	BaseURL      string
	ClientID     string
	ClientSecret string
}

type PayPalClient interface {
	CreateVaultSetupToken(ctx context.Context, req *CreateVaultSetupTokenRequest) (*CreateVaultSetupTokenResponse, error)
	CreateVaultPaymentToken(ctx context.Context, req *CreateVaultPaymentTokenRequest) (*CreateVaultPaymentTokenResponse, error)
}

type paypalClient struct {
	paymenttokens paymenttokens.ClientWithResponsesInterface
	orders        checkoutorders.ClientWithResponsesInterface
}

type Link struct {
	Href   string `json:"href"`
	Rel    string `json:"rel"`
	Method string `json:"method"`
}

// ExperienceContext Customizes the Vault creation flow experience for your customers.
type ExperienceContext struct {
	// BrandName The label that overrides the business name in the PayPal account on the PayPal site. The pattern is defined by an external party and supports Unicode.
	BrandName *string `json:"brand_name,omitempty"`

	// CancelUrl The URL where the customer is redirected after customer cancels or leaves the flow. It is a required field for contingency flows like PayPal wallet, 3DS.
	CancelUrl *string `json:"cancel_url,omitempty"`

	// Locale The [language tag](https://tools.ietf.org/html/bcp47#section-2) for the language in which to localize the error-related strings, such as messages, issues, and suggested actions. The tag is made up of the [ISO 639-2 language code](https://www.loc.gov/standards/iso639-2/php/code_list.php), the optional [ISO-15924 script tag](https://www.unicode.org/iso15924/codelists.html), and the [ISO-3166 alpha-2 country code](/api/rest/reference/country-codes/) or [M49 region code](https://unstats.un.org/unsd/methodology/m49/).
	Locale *string `json:"locale,omitempty"`

	// ReturnUrl The URL where the customer is redirected after customer approves leaves the flow. It is a required field for contingency flows like PayPal wallet, 3DS.
	ReturnUrl *string `json:"return_url,omitempty"`

	// ShippingPreference The shipping preference. This only applies to PayPal payment source.
	ShippingPreference *string `json:"shipping_preference,omitempty"`

	// VaultInstruction Vault Instruction on action to be performed after a successful payer approval.
	VaultInstruction *string `json:"vault_instruction,omitempty"`
}

func NewPayPalClient(config PayPalConfig, log logger.Logger) (PayPalClient, error) {
	client := ClientCredentials(config, log)

	// Create the generated client
	paymenttokens, err := paymenttokens.NewClientWithResponses(config.BaseURL, paymenttokens.WithHTTPClient(client))
	if err != nil {
		return nil, errors.Wrap(err, "failed to create PayPal client")
	}

	orders, err := checkoutorders.NewClientWithResponses(config.BaseURL, checkoutorders.WithHTTPClient(client))
	if err != nil {
		return nil, errors.Wrap(err, "failed to create PayPal client")
	}

	return &paypalClient{
		paymenttokens: paymenttokens,
		orders:        orders,
	}, nil
}

// createPalPalRequestID creates a unique request ID for PayPal API requests
func (c *paypalClient) createPalPalRequestID() string {
	return strconv.FormatInt(time.Now().UnixNano(), 10)
}

// handleErrorResponse handles error responses from the PayPal API
func (c *paypalClient) handleErrorResponse(statusCode int, body []byte) error {
	return errors.Errorf("PayPal API request failed with status %d: %s", statusCode, string(body))
}
