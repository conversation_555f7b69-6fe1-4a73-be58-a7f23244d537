package paypalapi

import (
	"context"

	paymenttokens "as-api/as/foundations/paypalapi/payment-tokens"

	"github.com/pkg/errors"
)

const (
	TokenTypeSetupToken = "SETUP_TOKEN"
)

type CardRequest = paymenttokens.CardRequest
type CardResponse = paymenttokens.CardResponse
type AddressPortable = paymenttokens.AddressPortable
type CountryCode = paymenttokens.CountryCode

type PaymentSourceRequest struct {
	Card *CardRequest `json:"card"`
}

type PaymentSourceResponse struct {
	Card *CardResponse `json:"card"`
}

type PaymentTokenSource struct {
	Token Token `json:"token"`
}

type Token struct {
	ID   string `json:"id"`
	Type string `json:"type"`
}

type Customer struct {
	ID string `json:"id"`
}

type PaymentTokenResponseSource struct {
	Card *CardResponse `json:"card"`
}

type CreateVaultSetupTokenRequest struct {
	Customer          *Customer            `json:"customer"`
	PaymentSource     PaymentSourceRequest `json:"payment_source"`
	ExperienceContext *ExperienceContext   `json:"experience_context"`
}

type CreateVaultSetupTokenResponse struct {
	ID            string                `json:"id"`
	Status        string                `json:"status"`
	PaymentSource PaymentSourceResponse `json:"payment_source"`
	Links         []Link                `json:"links"`
}

// CreateVaultSetupToken creates a setup token for a given payment source
func (c *paypalClient) CreateVaultSetupToken(ctx context.Context, req *CreateVaultSetupTokenRequest) (*CreateVaultSetupTokenResponse, error) {
	// Convert request to generated types
	setupTokenReq := convertToSetupTokenRequest(req)

	// Create PayPal-Request-Id
	params := &paymenttokens.SetupTokensCreateParams{
		PayPalRequestId: c.createPalPalRequestID(),
	}

	// Call the generated client with authentication
	resp, err := c.paymenttokens.SetupTokensCreateWithResponse(ctx, params, setupTokenReq)
	if err != nil {
		return nil, errors.Wrap(err, "create setup token request")
	}

	// Handle response
	if resp.StatusCode() >= 400 {
		return nil, c.handleErrorResponse(resp.StatusCode(), resp.Body)
	}

	// Convert successful response
	if resp.JSON200 != nil {
		return convertFromSetupTokenResponse(resp.JSON200), nil
	}
	if resp.JSON201 != nil {
		return convertFromSetupTokenResponse(resp.JSON201), nil
	}

	return nil, errors.Errorf("unexpected response status: %d", resp.StatusCode())
}

type CreateVaultPaymentTokenRequest struct {
	PaymentSource PaymentTokenSource `json:"payment_source"`
}

type CreateVaultPaymentTokenResponse struct {
	ID            string                     `json:"id"`
	Status        string                     `json:"status"`
	Customer      Customer                   `json:"customer"`
	PaymentSource PaymentTokenResponseSource `json:"payment_source"`
	Links         []Link                     `json:"links"`
}

// CreateVaultPaymentToken creates a payment token for a given payment source
func (c *paypalClient) CreateVaultPaymentToken(ctx context.Context, req *CreateVaultPaymentTokenRequest) (*CreateVaultPaymentTokenResponse, error) {
	// Convert request to generated types
	paymentTokenReq := convertToPaymentTokenRequest(req)

	// Create PayPal-Request-Id
	params := &paymenttokens.PaymentTokensCreateParams{
		PayPalRequestId: c.createPalPalRequestID(),
	}

	// Call the generated client with authentication
	resp, err := c.paymenttokens.PaymentTokensCreateWithResponse(ctx, params, paymentTokenReq)
	if err != nil {
		return nil, errors.Wrap(err, "create payment token request")
	}

	// Handle response
	if resp.StatusCode() >= 400 {
		return nil, c.handleErrorResponse(resp.StatusCode(), resp.Body)
	}

	// Convert successful response
	if resp.JSON200 != nil {
		return convertFromPaymentTokenResponse(resp.JSON200), nil
	}
	if resp.JSON201 != nil {
		return convertFromPaymentTokenResponse(resp.JSON201), nil
	}

	return nil, errors.Errorf("unexpected response status: %d", resp.StatusCode())
}
