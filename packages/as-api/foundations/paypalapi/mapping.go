package paypalapi

import (
	paymenttokens "as-api/as/foundations/paypalapi/payment-tokens"
	"as-api/as/pkg/helpers/pointer"
)

func convertFromSetupTokenResponse(resp *paymenttokens.SetupTokenResponse) *CreateVaultSetupTokenResponse {
	result := &CreateVaultSetupTokenResponse{
		ID:     *resp.Id,
		Status: string(*resp.Status),
		Links:  []Link{},
	}

	// Convert payment source
	if resp.PaymentSource != nil {
		result.PaymentSource.Card = resp.PaymentSource.Card
	}

	// Convert links
	for _, link := range pointer.Safe(resp.Links) {
		result.Links = append(result.Links, Link{
			Href:   link.Href,
			Rel:    link.Rel,
			Method: pointer.SafeString[string](link.Method),
		})
	}

	return result
}

func convertToSetupTokenRequest(req *CreateVaultSetupTokenRequest) paymenttokens.SetupTokenRequest {
	var setupReq paymenttokens.SetupTokenRequest

	cardReq := req.PaymentSource.Card
	if cardReq == nil {
		cardReq = &paymenttokens.CardRequest{}
	}

	// Enable SCA for the card
	cardReq.VerificationMethod = pointer.Ptr("SCA_ALWAYS")

	// Set the card request
	setupReq.PaymentSource.Card = cardReq

	// Set customer
	if req.Customer != nil {
		setupReq.Customer = &paymenttokens.Customer{
			Id: pointer.Ptr(req.Customer.ID),
		}
	}

	return setupReq
}

func convertFromPaymentTokenResponse(resp *paymenttokens.PaymentTokenResponse) *CreateVaultPaymentTokenResponse {
	result := &CreateVaultPaymentTokenResponse{
		ID:    *resp.Id,
		Links: []Link{},
	}

	// Convert customer
	if resp.Customer != nil && resp.Customer.Id != nil {
		result.Customer = Customer{
			ID: *resp.Customer.Id,
		}
	}

	// Convert payment source
	if resp.PaymentSource != nil {
		result.PaymentSource = PaymentTokenResponseSource{
			Card: resp.PaymentSource.Card,
		}
	}

	// Convert links
	for _, link := range *resp.Links {
		result.Links = append(result.Links, Link{
			Href:   link.Href,
			Rel:    link.Rel,
			Method: pointer.SafeString[string](link.Method),
		})
	}

	return result
}

func convertToPaymentTokenRequest(req *CreateVaultPaymentTokenRequest) paymenttokens.PaymentTokenRequest {
	var tokenReq paymenttokens.PaymentTokenRequest

	tokenReq.PaymentSource.Token = &paymenttokens.TokenIdRequest{
		Id:   req.PaymentSource.Token.ID,
		Type: paymenttokens.TokenIdRequestType(req.PaymentSource.Token.Type),
	}

	return tokenReq
}
