type: object
description: The tokenized payment source representing a request to vault a token
properties:
  id:
    type: string
    description: The PayPal-generated ID for the token
    minLength: 1
    maxLength: 255
    pattern: '^[0-9A-Za-z_-]+$'
    example: "7GH53639GA425732B"
  type:
    type: string
    description: The tokenization method that generated the ID
    minLength: 1
    maxLength: 255
    pattern: '^[0-9A-Z_-]+$'
    enum:
      - "SETUP_TOKEN"
    example: "SETUP_TOKEN"
required:
  - id
  - type
