<!DOCTYPE html>
<html>

<head>
    <!-- Add meta tags for mobile and IE -->
    <meta charset="utf-8" />
</head>

<body>
    <!-- Include the PayPal JavaScript SDK -->
    <script
        src="https://www.paypal.com/sdk/js?components=card-fields&client-id=AYHxfw4UijGwd9JkGNIpfSzHD3ywy0QGmctVXnpjhI6wmXUF3_i31dk36dcccWpXutZrWDzZkjxqh2Dt"></script>
    <div align="center"> or </div>
    <!-- Advanced credit and debit card payments form -->
    <div class='card_container'>
        <div id='card-number'></div>
        <div id='expiration-date'></div>
        <div id='cvv'></div>
        <div id='card-holder-name'></div>
        <label>
            <input type='checkbox' id='vault' name='vault' /> Vault
        </label>
        <br><br>
        <button value='submit' id='submit' class='btn'>Pay</button>
    </div> ​
    <!-- Implementation -->
    <script>
        const API_URL = "https://px818qlx-3000.asse.devtunnels.ms"
        const cardFields = paypal.CardFields({
            createVaultSetupToken: async () => {
                const data = {
                    payment_source: {
                        card: {
                            billing_address: {
                                "address_line_1": "2211 N First Street",
                                "address_line_2": "17.3.160",
                                "admin_area_1": "CA",
                                "admin_area_2": "San Jose",
                                "postal_code": "95131",
                                "country_code": "US"
                            },
                            verification_method: "SCA_ALWAYS",
                            experience_context: {
                                brand_name: "YourBrandName",
                                locale: "en-US",
                                return_url: "https://example.com/returnUrl",
                                cancel_url: "https://example.com/cancelUrl"
                            }
                        }
                    }
                }

                // Call your server API to generate a vaultSetupToken
                // and return it here as a string
                const result = await fetch(`${API_URL}/user/v1/vault/setup-token`, {
                    method: "POST",
                    body: JSON.stringify(data),
                })
                const { id } = await result.json();
                return id;
            },
            inputEvents: {
                onChange: (data) => {
                    console.log('onChange', data)
                }
            },
            onApprove: async (data) => {
                // Only for 3D Secure
                if (!data.liabilityShift) {
                    // Handle liability shift
                    alert("Liability shift is not available");
                }
                const result = await fetch(
                    `${API_URL}/user/v1/vault/payment-token`,
                    {
                        method: "POST",
                        body: JSON.stringify(data),
                    }
                );
                // id is the payment ID
                const { id } = await result.json();
            },
            onError: (error) => {
                console.error('Something went wrong:', error)
            }
        })
        // Check eligibility and display advanced credit and debit card payments
        if (cardFields.isEligible()) {
            cardFields.NameField().render("#card-holder-name");
            cardFields.NumberField().render("#card-number");
            cardFields.ExpiryField().render("#expiration-date");
            cardFields.CVVField().render("#cvv");
        } else {
            // Handle the workflow when credit and debit cards are not available
            alert("Credit and debit cards are not available");
        }
        const submitButton = document.getElementById("submit");
        submitButton.addEventListener("click", () => {
            cardFields
                .submit()
                .then(() => {
                    console.log("submit was successful");
                })
                .catch((error) => {
                    console.error("submit erred:", error);
                });
        });
    </script>
</body>

</html>